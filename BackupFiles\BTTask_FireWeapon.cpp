#include "BehaviorTree/Tasks/BTTask_FireWeapon.h"
#include "AIController.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "GameFramework/Character.h"
#include "GameFramework/Pawn.h"
#include "Engine/World.h"
#include "DrawDebugHelpers.h"
#include "Components/SkeletalMeshComponent.h"
#include "SquadMateAI.h"

UBTTask_FireWeapon::UBTTask_FireWeapon()
{
    NodeName = TEXT("Fire Weapon");
    bNotifyTick = true;
    bNotifyTaskFinished = true;
    
    // Set default blackboard keys
    TargetActorKey.SelectedKeyName = FName("TargetActor");
    HasLineOfSightKey.SelectedKeyName = FName("HasLineOfSight");
    AmmoCountKey.SelectedKeyName = FName("AmmoCount");
}

EBTNodeResult::Type UBTTask_FireWeapon::ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    // Initialize task memory
    InitializeTaskMemory(NodeMemory);
    
    // Get AI Controller and validate
    AAIController* AIController = OwnerComp.GetAIOwner();
    if (!AIController)
    {
        UE_LOG(LogSquadMateAI, Warning, TEXT("BTTask_FireWeapon: No AI Controller found"));
        return EBTNodeResult::Failed;
    }

    // Get blackboard component
    UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
    if (!BlackboardComp)
    {
        UE_LOG(LogSquadMateAI, Warning, TEXT("BTTask_FireWeapon: No Blackboard Component found"));
        return EBTNodeResult::Failed;
    }

    // Get target from blackboard
    AActor* Target = Cast<AActor>(BlackboardComp->GetValueAsObject(TargetActorKey.SelectedKeyName));
    if (!Target)
    {
        UE_LOG(LogSquadMateAI, Warning, TEXT("BTTask_FireWeapon: No valid target"));
        return EBTNodeResult::Failed;
    }

    // Validate target and firing conditions
    if (!ValidateTarget(OwnerComp, Target))
    {
        UE_LOG(LogSquadMateAI, Warning, TEXT("BTTask_FireWeapon: Target validation failed"));
        return EBTNodeResult::Failed;
    }

    // Check if we can fire
    if (!CanFireWeapon(OwnerComp))
    {
        UE_LOG(LogSquadMateAI, Warning, TEXT("BTTask_FireWeapon: Cannot fire weapon"));
        return EBTNodeResult::Failed;
    }

    // Initialize firing
    if (!InitializeFiring(OwnerComp, NodeMemory))
    {
        UE_LOG(LogSquadMateAI, Warning, TEXT("BTTask_FireWeapon: Failed to initialize firing"));
        return EBTNodeResult::Failed;
    }

    // Log fire start
    if (bLogFireEvents)
    {
        LogFireEvent(TEXT("Fire Started"), OwnerComp, Target);
    }

    return EBTNodeResult::InProgress;
}

EBTNodeResult::Type UBTTask_FireWeapon::AbortTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    CompleteFiring(OwnerComp, NodeMemory);
    return EBTNodeResult::Aborted;
}

void UBTTask_FireWeapon::TickTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaSeconds)
{
    FBTTask_FireWeaponMemory* TaskMemory = GetTaskMemory(NodeMemory);
    if (!TaskMemory)
    {
        FinishLatentTask(OwnerComp, EBTNodeResult::Failed);
        return;
    }

    // Update firing based on fire mode
    UpdateFiring(OwnerComp, NodeMemory, DeltaSeconds);

    // Check if we should stop firing
    float ElapsedTime = GetWorld()->GetTimeSeconds() - TaskMemory->FireStartTime;
    if (ElapsedTime >= MaxFireDuration)
    {
        CompleteFiring(OwnerComp, NodeMemory);
        FinishLatentTask(OwnerComp, EBTNodeResult::Succeeded);
        return;
    }

    // Check if target is still valid
    if (!TaskMemory->CurrentTarget.IsValid() || !ValidateTarget(OwnerComp, TaskMemory->CurrentTarget.Get()))
    {
        CompleteFiring(OwnerComp, NodeMemory);
        FinishLatentTask(OwnerComp, EBTNodeResult::Failed);
        return;
    }

    // Check ammo
    if (!HasSufficientAmmo(OwnerComp))
    {
        CompleteFiring(OwnerComp, NodeMemory);
        FinishLatentTask(OwnerComp, EBTNodeResult::Failed);
        return;
    }
}

FString UBTTask_FireWeapon::GetStaticDescription() const
{
    return FString::Printf(TEXT("Fire Weapon: %s\nMode: %s\nRange: %.1f"), 
                          *TargetActorKey.SelectedKeyName.ToString(),
                          *UEnum::GetValueAsString(FireMode),
                          MaxEngagementRange);
}

uint16 UBTTask_FireWeapon::GetInstanceMemorySize() const
{
    return sizeof(FBTTask_FireWeaponMemory);
}

bool UBTTask_FireWeapon::InitializeFiring(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    FBTTask_FireWeaponMemory* TaskMemory = GetTaskMemory(NodeMemory);
    if (!TaskMemory)
    {
        return false;
    }

    // Get target
    UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
    AActor* Target = Cast<AActor>(BlackboardComp->GetValueAsObject(TargetActorKey.SelectedKeyName));
    
    if (!Target)
    {
        return false;
    }

    // Initialize task memory
    TaskMemory->CurrentTarget = Target;
    TaskMemory->FireStartTime = GetWorld()->GetTimeSeconds();
    TaskMemory->LastShotTime = 0.0f;
    TaskMemory->LastBurstTime = 0.0f;
    TaskMemory->ShotsFiredInBurst = 0;
    TaskMemory->TotalShotsFired = 0;
    TaskMemory->bIsFiring = true;
    TaskMemory->bInBurstCooldown = false;
    TaskMemory->CurrentAccuracy = AccuracyModifier;

    // Start fire animation
    PlayFireAnimation(OwnerComp.GetAIOwner()->GetPawn());

    return true;
}

void UBTTask_FireWeapon::UpdateFiring(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime)
{
    FBTTask_FireWeaponMemory* TaskMemory = GetTaskMemory(NodeMemory);
    if (!TaskMemory || !TaskMemory->bIsFiring)
    {
        return;
    }

    // Execute firing based on mode
    switch (FireMode)
    {
        case EFireMode::SingleShot:
            if (TaskMemory->TotalShotsFired == 0)
            {
                FVector AimPoint = CalculateAimPoint(OwnerComp, TaskMemory->CurrentTarget.Get());
                ExecuteSingleShot(OwnerComp, AimPoint);
                TaskMemory->TotalShotsFired++;
            }
            break;

        case EFireMode::BurstFire:
            ExecuteBurstFire(OwnerComp, NodeMemory, DeltaTime);
            break;

        case EFireMode::FullAuto:
            ExecuteFullAuto(OwnerComp, NodeMemory, DeltaTime);
            break;

        case EFireMode::Suppressive:
            ExecuteFullAuto(OwnerComp, NodeMemory, DeltaTime);
            break;
    }
}

void UBTTask_FireWeapon::CompleteFiring(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    FBTTask_FireWeaponMemory* TaskMemory = GetTaskMemory(NodeMemory);
    if (TaskMemory)
    {
        TaskMemory->bIsFiring = false;
    }

    // Log fire completion
    if (bLogFireEvents)
    {
        LogFireEvent(TEXT("Fire Completed"), OwnerComp);
    }
}

void UBTTask_FireWeapon::ExecuteSingleShot(UBehaviorTreeComponent& OwnerComp, const FVector& AimPoint)
{
    AAIController* AIController = OwnerComp.GetAIOwner();
    if (!AIController || !AIController->GetPawn())
    {
        return;
    }

    FVector MuzzleLocation = GetWeaponMuzzleLocation(AIController->GetPawn());
    FVector AimDirection = (AimPoint - MuzzleLocation).GetSafeNormal();
    FVector FinalAimPoint = ApplyAccuracySpread(AimPoint, AccuracyModifier);

    // Perform line trace
    FHitResult HitResult;
    bool bHit = GetWorld()->LineTraceSingleByChannel(
        HitResult,
        MuzzleLocation,
        FinalAimPoint,
        ECC_Visibility
    );

    // Play effects
    PlayFireEffects(AIController->GetPawn(), MuzzleLocation);
    CreateBulletTrail(MuzzleLocation, bHit ? HitResult.Location : FinalAimPoint);

    // Apply damage if hit
    if (bHit && HitResult.GetActor())
    {
        // Apply damage logic here
        UE_LOG(LogSquadMateAI, Log, TEXT("BTTask_FireWeapon: Hit target %s"), *HitResult.GetActor()->GetName());
    }

    // Consume ammo
    ConsumeAmmo(OwnerComp);

    // Draw debug info
    if (bDrawDebugInfo)
    {
        DrawDebugFireInfo(GetWorld(), MuzzleLocation, FinalAimPoint, bHit);
    }
}

void UBTTask_FireWeapon::ExecuteBurstFire(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime)
{
    FBTTask_FireWeaponMemory* TaskMemory = GetTaskMemory(NodeMemory);
    if (!TaskMemory)
    {
        return;
    }

    float CurrentTime = GetWorld()->GetTimeSeconds();

    // Check if in burst cooldown
    if (TaskMemory->bInBurstCooldown)
    {
        if (CurrentTime - TaskMemory->LastBurstTime >= BurstCooldown)
        {
            TaskMemory->bInBurstCooldown = false;
            TaskMemory->ShotsFiredInBurst = 0;
        }
        return;
    }

    // Fire shots in burst
    if (TaskMemory->ShotsFiredInBurst < BurstSize)
    {
        if (CurrentTime - TaskMemory->LastShotTime >= FireRate)
        {
            FVector AimPoint = CalculateAimPoint(OwnerComp, TaskMemory->CurrentTarget.Get());
            ExecuteSingleShot(OwnerComp, AimPoint);
            
            TaskMemory->LastShotTime = CurrentTime;
            TaskMemory->ShotsFiredInBurst++;
            TaskMemory->TotalShotsFired++;
        }
    }
    else
    {
        // Start burst cooldown
        TaskMemory->bInBurstCooldown = true;
        TaskMemory->LastBurstTime = CurrentTime;
    }
}

void UBTTask_FireWeapon::ExecuteFullAuto(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime)
{
    FBTTask_FireWeaponMemory* TaskMemory = GetTaskMemory(NodeMemory);
    if (!TaskMemory)
    {
        return;
    }

    float CurrentTime = GetWorld()->GetTimeSeconds();

    // Fire at specified rate
    if (CurrentTime - TaskMemory->LastShotTime >= FireRate)
    {
        FVector AimPoint = CalculateAimPoint(OwnerComp, TaskMemory->CurrentTarget.Get());
        ExecuteSingleShot(OwnerComp, AimPoint);
        
        TaskMemory->LastShotTime = CurrentTime;
        TaskMemory->TotalShotsFired++;
    }
}

FVector UBTTask_FireWeapon::CalculateAimPoint(UBehaviorTreeComponent& OwnerComp, AActor* Target)
{
    if (!Target)
    {
        return FVector::ZeroVector;
    }

    // Simple aim point calculation - can be enhanced with prediction
    return Target->GetActorLocation();
}

FVector UBTTask_FireWeapon::ApplyAccuracySpread(const FVector& BaseAimPoint, float Accuracy)
{
    // Apply random spread based on accuracy
    float SpreadAngle = (1.0f - Accuracy) * 10.0f; // Max 10 degree spread
    FVector RandomOffset = FMath::VRand() * FMath::RandRange(0.0f, SpreadAngle);
    return BaseAimPoint + RandomOffset;
}

bool UBTTask_FireWeapon::ValidateTarget(UBehaviorTreeComponent& OwnerComp, AActor* Target)
{
    if (!Target)
    {
        return false;
    }

    AAIController* AIController = OwnerComp.GetAIOwner();
    if (!AIController || !AIController->GetPawn())
    {
        return false;
    }

    // Check distance
    float Distance = GetDistanceToTarget(AIController->GetPawn(), Target);
    if (Distance < MinEngagementRange || Distance > MaxEngagementRange)
    {
        return false;
    }

    // Check line of sight if required
    if (bRequireLineOfSight)
    {
        UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
        if (BlackboardComp && !BlackboardComp->GetValueAsBool(HasLineOfSightKey.SelectedKeyName))
        {
            return false;
        }
    }

    return true;
}

bool UBTTask_FireWeapon::HasSufficientAmmo(UBehaviorTreeComponent& OwnerComp)
{
    UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
    if (!BlackboardComp)
    {
        return false;
    }

    int32 CurrentAmmo = BlackboardComp->GetValueAsInt(AmmoCountKey.SelectedKeyName);
    return CurrentAmmo > 0;
}

void UBTTask_FireWeapon::ConsumeAmmo(UBehaviorTreeComponent& OwnerComp, int32 Amount)
{
    UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
    if (!BlackboardComp)
    {
        return;
    }

    int32 CurrentAmmo = BlackboardComp->GetValueAsInt(AmmoCountKey.SelectedKeyName);
    BlackboardComp->SetValueAsInt(AmmoCountKey.SelectedKeyName, FMath::Max(0, CurrentAmmo - Amount));
}

bool UBTTask_FireWeapon::CanFireWeapon(UBehaviorTreeComponent& OwnerComp)
{
    return HasSufficientAmmo(OwnerComp);
}

void UBTTask_FireWeapon::PlayFireEffects(AActor* Shooter, const FVector& MuzzleLocation)
{
    if (!Shooter || !GetWorld())
    {
        return;
    }

    // Play muzzle flash effect
    if (MuzzleFlashEffect)
    {
        // UGameplayStatics::SpawnEmitterAtLocation(GetWorld(), MuzzleFlashEffect, MuzzleLocation);
    }

    // Play fire sound
    if (FireSound)
    {
        // UGameplayStatics::PlaySoundAtLocation(GetWorld(), FireSound, MuzzleLocation);
    }
}

void UBTTask_FireWeapon::PlayFireAnimation(AActor* Shooter)
{
    if (!Shooter)
    {
        return;
    }

    if (ACharacter* Character = Cast<ACharacter>(Shooter))
    {
        if (FireMontage)
        {
            Character->PlayAnimMontage(FireMontage);
        }
    }
}

void UBTTask_FireWeapon::CreateBulletTrail(const FVector& Start, const FVector& End)
{
    // Create bullet trail effect - implementation depends on your effect system
}

float UBTTask_FireWeapon::GetDistanceToTarget(AActor* From, AActor* To)
{
    if (!From || !To)
    {
        return FLT_MAX;
    }

    return FVector::Dist(From->GetActorLocation(), To->GetActorLocation());
}

bool UBTTask_FireWeapon::HasLineOfSightToTarget(AActor* From, AActor* To)
{
    if (!From || !To)
    {
        return false;
    }

    FHitResult HitResult;
    FVector Start = From->GetActorLocation();
    FVector End = To->GetActorLocation();

    bool bHit = GetWorld()->LineTraceSingleByChannel(
        HitResult,
        Start,
        End,
        ECC_Visibility
    );

    return !bHit || HitResult.GetActor() == To;
}

FVector UBTTask_FireWeapon::GetWeaponMuzzleLocation(AActor* Actor)
{
    if (!Actor)
    {
        return FVector::ZeroVector;
    }

    // Simple implementation - return actor location + forward offset
    return Actor->GetActorLocation() + Actor->GetActorForwardVector() * 100.0f;
}

void UBTTask_FireWeapon::LogFireEvent(const FString& Event, UBehaviorTreeComponent& OwnerComp, AActor* Target)
{
    FString TargetName = Target ? Target->GetName() : TEXT("None");
    UE_LOG(LogSquadMateAI, Log, TEXT("BTTask_FireWeapon: %s - Target: %s"), *Event, *TargetName);
}

void UBTTask_FireWeapon::DrawDebugFireInfo(UWorld* World, const FVector& Start, const FVector& End, bool bHit)
{
    if (!World)
    {
        return;
    }

    FColor LineColor = bHit ? FColor::Red : FColor::Orange;
    DrawDebugLine(World, Start, End, LineColor, false, 0.5f, 0, 2.0f);
    
    if (bHit)
    {
        DrawDebugSphere(World, End, 25.0f, 8, FColor::Red, false, 0.5f);
    }
}

void UBTTask_FireWeapon::InitializeTaskMemory(uint8* NodeMemory)
{
    FBTTask_FireWeaponMemory* TaskMemory = new(NodeMemory) FBTTask_FireWeaponMemory();
}

void UBTTask_FireWeapon::CleanupTaskMemory(uint8* NodeMemory)
{
    FBTTask_FireWeaponMemory* TaskMemory = GetTaskMemory(NodeMemory);
    if (TaskMemory)
    {
        TaskMemory->~FBTTask_FireWeaponMemory();
    }
}

UBTTask_FireWeapon::FBTTask_FireWeaponMemory* UBTTask_FireWeapon::GetTaskMemory(uint8* NodeMemory)
{
    return reinterpret_cast<FBTTask_FireWeaponMemory*>(NodeMemory);
}

// Static utility functions
bool UBTTask_FireWeapon::CanFireAtTarget(AActor* Shooter, AActor* Target, float MaxRange)
{
    if (!Shooter || !Target)
    {
        return false;
    }

    float Distance = FVector::Dist(Shooter->GetActorLocation(), Target->GetActorLocation());
    return Distance <= MaxRange;
}

float UBTTask_FireWeapon::CalculateAccuracy(AActor* Shooter, AActor* Target, float BaseAccuracy)
{
    if (!Shooter || !Target)
    {
        return 0.0f;
    }

    // Simple accuracy calculation based on distance
    float Distance = FVector::Dist(Shooter->GetActorLocation(), Target->GetActorLocation());
    float DistanceModifier = FMath::Clamp(1.0f - (Distance / 1000.0f), 0.1f, 1.0f);
    
    return BaseAccuracy * DistanceModifier;
}
