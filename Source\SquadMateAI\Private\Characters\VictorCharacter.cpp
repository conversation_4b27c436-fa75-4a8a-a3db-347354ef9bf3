#include "Characters/VictorCharacter.h"
#include "AI/TDMAIController.h"
#include "Components/HealthComponent.h"
#include "Components/InventoryComponent.h"
#include "Components/SquadRoleComponent.h"
#include "Components/ReviveComponent.h"
#include "Components/DecisionLoggerComponent.h"
#include "Weapons/TDMWeaponSystem.h"
#include "Components/SkeletalMeshComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/CapsuleComponent.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Animation/AnimInstance.h"
#include "Animation/AnimMontage.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "TimerManager.h"

AVictorCharacter::AVictorCharacter()
{
    PrimaryActorTick.bCanEverTick = true;

    // Initialize Components
    HealthComponent = CreateDefaultSubobject<UHealthComponent>(TEXT("HealthComponent"));
    InventoryComponent = CreateDefaultSubobject<UInventoryComponent>(TEXT("InventoryComponent"));
    SquadRoleComponent = CreateDefaultSubobject<USquadRoleComponent>(TEXT("SquadRoleComponent"));
    WeaponSystem = CreateDefaultSubobject<UTDMWeaponSystem>(TEXT("WeaponSystem"));
    ReviveComponent = CreateDefaultSubobject<UReviveComponent>(TEXT("ReviveComponent"));
    DecisionLogger = CreateDefaultSubobject<UDecisionLoggerComponent>(TEXT("DecisionLogger"));

    // Setup Victor Mesh (replace default mesh)
    VictorMesh = GetMesh();
    if (VictorMesh)
    {
        VictorMesh->SetRelativeLocation(FVector(0.0f, 0.0f, -90.0f));
        VictorMesh->SetRelativeRotation(FRotator(0.0f, -90.0f, 0.0f));
    }

    // Create weapon mesh component
    WeaponMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("WeaponMesh"));
    WeaponMesh->SetupAttachment(VictorMesh, TEXT("WeaponSocket"));

    // Create equipment mesh component
    EquipmentMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("EquipmentMesh"));
    EquipmentMesh->SetupAttachment(VictorMesh);

    // Initialize default values
    CurrentAnimationState = EVictorAnimationState::Idle;
    CurrentMovementState = EVictorMovementState::Standing;
    CurrentCombatState = EVictorCombatState::Passive;
    TeamID = 0;

    // Movement speeds (PUBGM-like values)
    WalkSpeed = 150.0f;
    RunSpeed = 300.0f;
    CrouchSpeed = 100.0f;
    ProneSpeed = 50.0f;
    SlideSpeed = 400.0f;
    SlideDuration = 1.5f;

    // Combat state
    bIsAiming = false;
    bIsFiring = false;
    bIsReloading = false;
    bIsPeeking = false;
    bIsSuppressed = false;
    AimRotation = FRotator::ZeroRotator;

    // Interaction state
    bIsReviving = false;
    bIsBeingRevived = false;
    ReviveProgress = 0.0f;

    // Internal state
    SlideStartTime = 0.0f;
    SlideDirection = FVector::ZeroVector;
    bIsSliding = false;
    ReviveTarget = nullptr;
    ReviveStartTime = 0.0f;

    // Configure movement component
    ConfigureMovement();
}

void AVictorCharacter::BeginPlay()
{
    Super::BeginPlay();
    
    InitializeComponents();
    SetupVictorMesh();
    LoadVictorAssets();
}

void AVictorCharacter::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    UpdateAnimationState();
    UpdateMovementSpeed();
    HandleSlideMovement(DeltaTime);
    HandleReviveProgress(DeltaTime);
    UpdateAimRotation();
    UpdateCombatState();
}

void AVictorCharacter::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
    Super::SetupPlayerInputComponent(PlayerInputComponent);
    // Input is handled by AI Controller, not player input
}

void AVictorCharacter::InitializeComponents()
{
    // Initialize Health Component
    if (HealthComponent)
    {
        HealthComponent->SetMaxHealth(100.0f);
        HealthComponent->ResetHealth();
        
        // Bind health events
        HealthComponent->OnHealthChanged.AddDynamic(this, &AVictorCharacter::OnTakeDamage);
        HealthComponent->OnDeath.AddDynamic(this, &AVictorCharacter::OnDeath);
    }

    // Initialize Revive Component
    if (ReviveComponent)
    {
        ReviveComponent->SetReviveTime(5.0f); // 5 seconds to revive
        ReviveComponent->OnReviveCompleted.AddDynamic(this, &AVictorCharacter::OnRevived);
    }

    // Initialize Weapon System
    if (WeaponSystem)
    {
        WeaponSystem->InitializeForTDM();
    }
}

void AVictorCharacter::SetupVictorMesh()
{
    if (!VictorMesh)
        return;

    // Load Victor model from the victor folder
    static ConstructorHelpers::FObjectFinder<USkeletalMesh> VictorMeshAsset(
        TEXT("/Game/Victor/Victore_model_fix")
    );
    
    if (VictorMeshAsset.Succeeded())
    {
        VictorMesh->SetSkeletalMesh(VictorMeshAsset.Object);
    }

    // Setup animation blueprint
    static ConstructorHelpers::FClassFinder<UAnimInstance> VictorAnimBPClass(
        TEXT("/Game/Characters/Victor/ABP_Victor")
    );
    
    if (VictorAnimBPClass.Succeeded())
    {
        VictorMesh->SetAnimInstanceClass(VictorAnimBPClass.Class);
    }
}

void AVictorCharacter::LoadVictorAssets()
{
    ApplyVictorTextures();
    SetupVictorAnimations();
}

void AVictorCharacter::ApplyVictorTextures()
{
    if (!VictorMesh)
        return;

    // Load textures from victor/textures folder
    static ConstructorHelpers::FObjectFinder<UMaterialInterface> Material0(
        TEXT("/Game/Victor/Materials/M_Victor_mat0")
    );
    static ConstructorHelpers::FObjectFinder<UMaterialInterface> Material1(
        TEXT("/Game/Victor/Materials/M_Victor_mat0_001")
    );
    static ConstructorHelpers::FObjectFinder<UMaterialInterface> Material2(
        TEXT("/Game/Victor/Materials/M_Victor_mat0_002")
    );

    if (Material0.Succeeded())
    {
        VictorMesh->SetMaterial(0, Material0.Object);
    }
    if (Material1.Succeeded())
    {
        VictorMesh->SetMaterial(1, Material1.Object);
    }
    if (Material2.Succeeded())
    {
        VictorMesh->SetMaterial(2, Material2.Object);
    }
}

void AVictorCharacter::SetupVictorAnimations()
{
    // Animation setup will be handled in Blueprint
    // This method can be used for C++ animation configuration if needed
}

void AVictorCharacter::ConfigureMovement()
{
    UCharacterMovementComponent* MovementComp = GetCharacterMovement();
    if (!MovementComp)
        return;

    // Configure movement for PUBGM-style mechanics
    MovementComp->MaxWalkSpeed = RunSpeed;
    MovementComp->MaxWalkSpeedCrouched = CrouchSpeed;
    MovementComp->JumpZVelocity = 420.0f;
    MovementComp->AirControl = 0.2f;
    MovementComp->GroundFriction = 8.0f;
    MovementComp->MaxAcceleration = 2048.0f;
    MovementComp->BrakingDecelerationWalking = 2048.0f;
    MovementComp->BrakingDecelerationFalling = 1500.0f;
    
    // Enable crouching
    MovementComp->GetNavAgentPropertiesRef().bCanCrouch = true;
    MovementComp->CrouchedHalfHeight = 40.0f;
}

void AVictorCharacter::UpdateAnimationState()
{
    if (!VictorMesh || !VictorMesh->GetAnimInstance())
        return;

    UAnimInstance* AnimInstance = VictorMesh->GetAnimInstance();
    
    // Update animation variables based on current state
    float Speed = GetVelocity().Size();
    bool bIsMoving = Speed > 10.0f;
    
    // Set animation state based on current conditions
    if (bIsReviving)
    {
        SetAnimationState(EVictorAnimationState::Reviving);
    }
    else if (bIsBeingRevived)
    {
        SetAnimationState(EVictorAnimationState::BeingRevived);
    }
    else if (!IsAlive())
    {
        if (IsDown())
        {
            SetAnimationState(EVictorAnimationState::Down);
        }
        else
        {
            SetAnimationState(EVictorAnimationState::Dead);
        }
    }
    else if (bIsReloading)
    {
        SetAnimationState(EVictorAnimationState::Reloading);
    }
    else if (bIsFiring)
    {
        SetAnimationState(EVictorAnimationState::Firing);
    }
    else if (bIsAiming)
    {
        SetAnimationState(EVictorAnimationState::Aiming);
    }
    else if (bIsSliding)
    {
        SetAnimationState(EVictorAnimationState::Sliding);
    }
    else if (CurrentMovementState == EVictorMovementState::Prone)
    {
        SetAnimationState(EVictorAnimationState::Prone);
    }
    else if (CurrentMovementState == EVictorMovementState::Crouched)
    {
        SetAnimationState(EVictorAnimationState::Crouching);
    }
    else if (bIsMoving)
    {
        if (Speed > RunSpeed * 0.8f)
        {
            SetAnimationState(EVictorAnimationState::Running);
        }
        else
        {
            SetAnimationState(EVictorAnimationState::Walking);
        }
    }
    else
    {
        SetAnimationState(EVictorAnimationState::Idle);
    }
}

void AVictorCharacter::UpdateMovementSpeed()
{
    UCharacterMovementComponent* MovementComp = GetCharacterMovement();
    if (!MovementComp)
        return;

    switch (CurrentMovementState)
    {
        case EVictorMovementState::Standing:
            MovementComp->MaxWalkSpeed = RunSpeed;
            break;
        case EVictorMovementState::Crouched:
            MovementComp->MaxWalkSpeed = CrouchSpeed;
            break;
        case EVictorMovementState::Prone:
            MovementComp->MaxWalkSpeed = ProneSpeed;
            break;
        case EVictorMovementState::Sliding:
            MovementComp->MaxWalkSpeed = SlideSpeed;
            break;
    }
}

void AVictorCharacter::HandleSlideMovement(float DeltaTime)
{
    if (!bIsSliding)
        return;

    float ElapsedTime = GetWorld()->GetTimeSeconds() - SlideStartTime;
    if (ElapsedTime >= SlideDuration)
    {
        StopSlide();
        return;
    }

    // Apply slide movement
    FVector SlideVelocity = SlideDirection * SlideSpeed * (1.0f - ElapsedTime / SlideDuration);
    GetCharacterMovement()->Velocity = FVector(SlideVelocity.X, SlideVelocity.Y, GetCharacterMovement()->Velocity.Z);
}

void AVictorCharacter::HandleReviveProgress(float DeltaTime)
{
    if (!bIsReviving || !ReviveTarget)
        return;

    float ElapsedTime = GetWorld()->GetTimeSeconds() - ReviveStartTime;
    float ReviveTime = ReviveComponent ? ReviveComponent->GetReviveTime() : 5.0f;
    
    ReviveProgress = FMath::Clamp(ElapsedTime / ReviveTime, 0.0f, 1.0f);

    if (ReviveProgress >= 1.0f)
    {
        // Complete revive
        if (ReviveTarget->ReviveComponent)
        {
            ReviveTarget->ReviveComponent->CompleteRevive();
        }
        StopRevive();
    }
}

void AVictorCharacter::UpdateAimRotation()
{
    if (!bIsAiming)
        return;

    // Get aim direction from AI controller
    if (ATDMAIController* AIController = GetTDMAIController())
    {
        if (AActor* Target = AIController->GetCurrentTarget())
        {
            FVector AimDirection = (Target->GetActorLocation() - GetActorLocation()).GetSafeNormal();
            AimRotation = AimDirection.Rotation();
        }
    }
}

void AVictorCharacter::UpdateCombatState()
{
    if (ATDMAIController* AIController = GetTDMAIController())
    {
        ETDMTacticState TacticState = AIController->GetCurrentTactic();
        
        switch (TacticState)
        {
            case ETDMTacticState::Patrol:
                SetCombatState(EVictorCombatState::Passive);
                break;
            case ETDMTacticState::Engage:
                SetCombatState(EVictorCombatState::Combat);
                break;
            case ETDMTacticState::Flank:
                SetCombatState(EVictorCombatState::Flanking);
                break;
            case ETDMTacticState::Suppress:
                SetCombatState(EVictorCombatState::Suppressed);
                break;
            default:
                SetCombatState(EVictorCombatState::Alert);
                break;
        }
    }
}

// Public Interface Implementation
void AVictorCharacter::SetAnimationState(EVictorAnimationState NewState)
{
    if (CurrentAnimationState != NewState)
    {
        CurrentAnimationState = NewState;
        // Animation state changes are handled by the Animation Blueprint
    }
}

void AVictorCharacter::SetMovementState(EVictorMovementState NewState)
{
    if (CurrentMovementState != NewState)
    {
        CurrentMovementState = NewState;
        ApplyMovementState();
    }
}

void AVictorCharacter::SetCombatState(EVictorCombatState NewState)
{
    CurrentCombatState = NewState;
}

void AVictorCharacter::SetTeamID(int32 NewTeamID)
{
    TeamID = NewTeamID;
    UpdateTeamVisuals();
}

void AVictorCharacter::ApplyMovementState()
{
    UCharacterMovementComponent* MovementComp = GetCharacterMovement();
    if (!MovementComp)
        return;

    switch (CurrentMovementState)
    {
        case EVictorMovementState::Crouched:
            MovementComp->bWantsToCrouch = true;
            break;
        case EVictorMovementState::Standing:
            MovementComp->bWantsToCrouch = false;
            break;
        case EVictorMovementState::Prone:
            // Prone implementation would require custom movement mode
            break;
        case EVictorMovementState::Sliding:
            // Sliding is handled separately
            break;
    }
}

void AVictorCharacter::UpdateTeamVisuals()
{
    // Update team-based visual elements (colors, indicators, etc.)
    FLinearColor TeamColor = (TeamID == 0) ? FLinearColor::Blue : FLinearColor::Red;
    SetTeamColor(TeamColor);
}

void AVictorCharacter::SetTeamColor(const FLinearColor& Color)
{
    // Apply team color to materials or UI elements
    // This would be implemented based on your visual design
}

// Movement Actions
void AVictorCharacter::StartCrouch()
{
    SetMovementState(EVictorMovementState::Crouched);
}

void AVictorCharacter::StopCrouch()
{
    SetMovementState(EVictorMovementState::Standing);
}

void AVictorCharacter::StartProne()
{
    SetMovementState(EVictorMovementState::Prone);
}

void AVictorCharacter::StopProne()
{
    SetMovementState(EVictorMovementState::Standing);
}

void AVictorCharacter::StartSlide()
{
    if (bIsSliding || GetVelocity().Size() < 100.0f)
        return;

    bIsSliding = true;
    SlideStartTime = GetWorld()->GetTimeSeconds();
    SlideDirection = GetVelocity().GetSafeNormal();
    SetMovementState(EVictorMovementState::Sliding);
}

void AVictorCharacter::StopSlide()
{
    if (!bIsSliding)
        return;

    bIsSliding = false;
    SetMovementState(EVictorMovementState::Standing);
}
