i have added victor 3d model with textures and tdm map 3d model here so lets build production ready ai agent which play 5 agent v 5 agent in tdm match in pubgm mechnaism Here is a complete breakdown of the **TDM (Team Deathmatch) mode in PUBG Mobile (PUBGM) 5v5 Arena** — covering **gameplay mechanics, inventory, controls, spawn logic, gunplay, movement**, and more. This is perfect for building a realistic AI teammate or training agent:

---

## 🎮 OVERVIEW: PUBG Mobile 5v5 TDM (Arena - Warehouse)

| Aspect       | Detail                                                 |
| ------------ | ------------------------------------------------------ |
| Mode         | Team Deathmatch (5v5)                                  |
| Objective    | First team to reach **40 kills** or highest in 10 mins |
| Respawn      | Instant after death (\~3s delay)                       |
| Map          | Compact indoor warehouse, symmetric sides              |
| Player Count | 5 players per team                                     |
| Time Limit   | 10 minutes or until kill target reached                |

---

## 🔫 WEAPONS & INVENTORY

### 🔹 Starting Loadout

* Players can **choose loadouts** before entering:

  * **ARs**: M416, AKM, Scar-L, UMP45
  * **SMGs**: Uzi, Vector
  * **Shotguns**: S12K
  * **Snipers**: Rare (SKS, Kar98, M24 only in pickups)
  * **Pistols**: Optional
* **Melee & grenades**: No grenades in standard TDM. Melee optional.

### 🔹 Inventory Rules

* Infinite ammo (auto refill)
* Auto-pickup enabled
* Players can switch loadouts **after respawn**

---

## 🧍‍♂️ PLAYER ACTIONS

### 🎯 Basic Movement

* **Walk / Run** (joystick)
* **Jump**
* **Crouch**
* **Slide** (while sprinting + crouch button)
* **Prone**
* **Vault** (over boxes, ledges)
* **Sprint Toggle**

### 🧍‍♂️ Advanced Tactics

* **Peek & Fire (Lean)**:

  * Left/Right peek from cover (with ADS or hipfire)
  * Can be toggled in settings
* **Hipfire / ADS / Scope**

  * Toggle firing style
  * Different spread accuracy
* **Drop-shot**: Prone during fire for surprise

---

## 🧠 GAME MECHANICS

### 🔁 Spawn System

* Players respawn on their half of the map
* Protected with **spawn shield** (\~3 seconds)
* **No spawn flip** unless team pushed too far

### ❤️ Health & Damage

* **HP**: 100
* **Auto-heal on respawn**
* **No healing items**
* **No armor helmets** in standard TDM
* Damage is based on gun stats (M416 does \~40–45 HP per chest shot)

### 🧠 Hitboxes

* Headshots = 2x damage
* Chest = 1x
* Limbs = 0.5x

---

## 🧩 MAP STRUCTURE (Warehouse Arena)

* **Three main lanes**: left, center, right
* **Cover elements**: crates, boxes, barrels
* **Elevated ramps** on the side
* **Spawn zones** behind walls
* **No interactive objects** (like doors)

---

## 🕹️ UI Elements

| UI Component   | Behavior                                                      |
| -------------- | ------------------------------------------------------------- |
| Minimap        | Shows teammates and enemy gunfire (not always enemy position) |
| Killfeed       | Shows recent kills and weapons used                           |
| Scoreboard     | Kills / deaths per player, updated live                       |
| Timer          | Countdown from 10:00                                          |
| Loadout button | Available only in respawn screen                              |

---

## 🤖 Ideal AI Agent Behavior

* **Movement**: sprint → slide → cover
* **Decision tree**:

  * If see enemy → shoot
  * If HP < 40 → retreat to cover
  * If ally nearby and downed → revive
  * If team losing → push middle
  * If flank open → rotate side
* **Cover usage**: peek → burst fire → retreat
* **Adaptive tactics**: if overrun, switch flank or hold angle
* **Role-based**:

  * Sniper: hold back and cover lanes
  * SMG: aggressive push
  * AR: balance fire and mid-range support

---

## 🎓 Additional Notes

* No fall damage
* No looting system (predefined loadouts)
* Aim assist is available for beginners
* Bots often populate lower-rank matches, but not in ranked Arena

---




