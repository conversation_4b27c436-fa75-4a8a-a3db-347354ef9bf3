#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Character.h"
#include "GameplayTagContainer.h"
#include "Components/TimelineComponent.h"
#include "SquadMateCharacter.generated.h"

class UHealthComponent;
class UInventoryComponent;
class UReviveComponent;
class USquadRoleComponent;
class UDecisionLoggerComponent;
class ASquadMateAIController;

UENUM(BlueprintType)
enum class ECharacterStance : uint8
{
    Standing    UMETA(DisplayName = "Standing"),
    Crouching   UMETA(DisplayName = "Crouching"),
    Prone       UMETA(DisplayName = "Prone"),
    PeekLeft    UMETA(DisplayName = "Peek Left"),
    PeekRight   UMETA(DisplayName = "Peek Right")
};

UENUM(BlueprintType)
enum class EWeaponState : uint8
{
    Idle        UMETA(DisplayName = "Idle"),
    Aiming      UMETA(DisplayName = "Aiming"),
    Firing      UMETA(DisplayName = "Firing"),
    Reloading   UMETA(DisplayName = "Reloading")
};

USTRUCT(BlueprintType)
struct FWeaponData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString WeaponName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 Damage = 30;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float Range = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float FireRate = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 MagazineSize = 30;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float ReloadTime = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIsAutomatic = true;

    FWeaponData()
    {
        WeaponName = TEXT("AssaultRifle");
        Damage = 30;
        Range = 1000.0f;
        FireRate = 0.1f;
        MagazineSize = 30;
        ReloadTime = 2.0f;
        bIsAutomatic = true;
    }
};

UCLASS(BlueprintType, Blueprintable)
class SQUADMATEAI_API ASquadMateCharacter : public ACharacter
{
    GENERATED_BODY()

public:
    ASquadMateCharacter();

protected:
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;
    virtual void SetupPlayerInputComponent(class UInputComponent* PlayerInputComponent) override;

    // Core Components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    UHealthComponent* HealthComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    UInventoryComponent* InventoryComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    UReviveComponent* ReviveComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    USquadRoleComponent* SquadRoleComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    UDecisionLoggerComponent* DecisionLoggerComponent;

    // Weapon System
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon")
    FWeaponData CurrentWeapon;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Weapon")
    UStaticMeshComponent* WeaponMesh;

    UPROPERTY(BlueprintReadWrite, Category = "Weapon")
    EWeaponState WeaponState;

    // Character State
    UPROPERTY(BlueprintReadWrite, Category = "Character")
    ECharacterStance CurrentStance;

    UPROPERTY(BlueprintReadWrite, Category = "Character")
    bool bIsAiming;

    UPROPERTY(BlueprintReadWrite, Category = "Character")
    bool bIsFiring;

    UPROPERTY(BlueprintReadWrite, Category = "Character")
    bool bIsReloading;

    // Team System
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team")
    int32 TeamID = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team")
    FLinearColor TeamColor = FLinearColor::Blue;

    // Animation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    class UAnimMontage* FireMontage;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    class UAnimMontage* ReloadMontage;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    class UAnimMontage* ReviveMontage;

    // Gameplay Tags
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GameplayTags")
    FGameplayTagContainer CharacterTags;

    // Timeline Components for smooth transitions
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Animation")
    UTimelineComponent* StanceTimeline;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    UCurveFloat* StanceCurve;

public:
    // Public Interface
    UFUNCTION(BlueprintCallable, Category = "Character")
    void SetStance(ECharacterStance NewStance);

    UFUNCTION(BlueprintCallable, Category = "Character")
    ECharacterStance GetStance() const { return CurrentStance; }

    UFUNCTION(BlueprintCallable, Category = "Weapon")
    void StartFiring();

    UFUNCTION(BlueprintCallable, Category = "Weapon")
    void StopFiring();

    UFUNCTION(BlueprintCallable, Category = "Weapon")
    void StartAiming();

    UFUNCTION(BlueprintCallable, Category = "Weapon")
    void StopAiming();

    UFUNCTION(BlueprintCallable, Category = "Weapon")
    void Reload();

    UFUNCTION(BlueprintCallable, Category = "Weapon")
    bool CanFire() const;

    UFUNCTION(BlueprintCallable, Category = "Weapon")
    void FireWeapon();

    UFUNCTION(BlueprintCallable, Category = "Team")
    int32 GetTeamID() const { return TeamID; }

    UFUNCTION(BlueprintCallable, Category = "Team")
    void SetTeamID(int32 NewTeamID);

    UFUNCTION(BlueprintCallable, Category = "Team")
    bool IsEnemy(ASquadMateCharacter* OtherCharacter) const;

    UFUNCTION(BlueprintCallable, Category = "Team")
    bool IsAlly(ASquadMateCharacter* OtherCharacter) const;

    // Interaction System
    UFUNCTION(BlueprintCallable, Category = "Interaction")
    void StartReviving(ASquadMateCharacter* Target);

    UFUNCTION(BlueprintCallable, Category = "Interaction")
    void StopReviving();

    UFUNCTION(BlueprintCallable, Category = "Interaction")
    bool CanRevive(ASquadMateCharacter* Target) const;

    // Movement and Positioning
    UFUNCTION(BlueprintCallable, Category = "Movement")
    void PeekLeft();

    UFUNCTION(BlueprintCallable, Category = "Movement")
    void PeekRight();

    UFUNCTION(BlueprintCallable, Category = "Movement")
    void StopPeeking();

    UFUNCTION(BlueprintCallable, Category = "Movement")
    void GoToCover(FVector CoverLocation);

    // AI Interface
    UFUNCTION(BlueprintCallable, Category = "AI")
    ASquadMateAIController* GetSquadMateAIController() const;

    UFUNCTION(BlueprintCallable, Category = "AI")
    void SetAITarget(AActor* Target);

    UFUNCTION(BlueprintCallable, Category = "AI")
    AActor* GetAITarget() const;

    // Damage System
    UFUNCTION(BlueprintCallable, Category = "Damage")
    virtual float TakeDamage(float DamageAmount, struct FDamageEvent const& DamageEvent, 
                           class AController* EventInstigator, AActor* DamageCauser) override;

    UFUNCTION(BlueprintCallable, Category = "Damage")
    void Die();

    UFUNCTION(BlueprintCallable, Category = "Damage")
    void Revive();

    // Utility Functions
    UFUNCTION(BlueprintCallable, Category = "Utility")
    FVector GetWeaponMuzzleLocation() const;

    UFUNCTION(BlueprintCallable, Category = "Utility")
    FRotator GetAimRotation() const;

    UFUNCTION(BlueprintCallable, Category = "Utility")
    bool HasLineOfSightTo(AActor* Target) const;

protected:
    // Internal Methods
    void InitializeComponents();
    void SetupWeapon();
    void UpdateAnimationState();
    void HandleStanceTransition();
    void ProcessWeaponFiring();
    void ApplyRecoil();

    // Timeline Callbacks
    UFUNCTION()
    void OnStanceTimelineUpdate(float Value);

    UFUNCTION()
    void OnStanceTimelineFinished();

    // Animation Callbacks
    UFUNCTION()
    void OnFireMontageEnded(UAnimMontage* Montage, bool bInterrupted);

    UFUNCTION()
    void OnReloadMontageEnded(UAnimMontage* Montage, bool bInterrupted);

    UFUNCTION()
    void OnReviveMontageEnded(UAnimMontage* Montage, bool bInterrupted);

private:
    // Internal State
    FTimerHandle FireTimerHandle;
    FTimerHandle ReloadTimerHandle;
    FTimerHandle ReviveTimerHandle;
    
    AActor* CurrentTarget;
    ASquadMateCharacter* ReviveTarget;
    
    float LastFireTime;
    float StanceTransitionAlpha;
    FVector OriginalCapsuleLocation;
    
    // Weapon System
    void StartFireTimer();
    void StopFireTimer();
    bool CanStartFiring() const;
    void ProcessSingleShot();
    
    // Animation System
    void PlayFireAnimation();
    void PlayReloadAnimation();
    void PlayReviveAnimation();
    
    // Stance System
    void UpdateCapsuleForStance();
    float GetStanceHeight(ECharacterStance Stance) const;
    
    // Combat Utilities
    bool IsValidTarget(AActor* Target) const;
    float CalculateDamageToTarget(AActor* Target) const;
    void ApplyDamageToTarget(AActor* Target, float Damage);
};
