#include "BehaviorTree/Tasks/BTTask_ReviveAlly.h"
#include "AIController.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "GameFramework/Character.h"
#include "GameFramework/Pawn.h"
#include "Engine/World.h"
#include "DrawDebugHelpers.h"
#include "AI/SquadMateAIController.h"
#include "Components/HealthComponent.h"
#include "Components/ReviveComponent.h"
#include "SquadMateAI.h"

UBTTask_ReviveAlly::UBTTask_ReviveAlly()
{
    NodeName = TEXT("Revive Ally");
    bNotifyTick = true;
    bNotifyTaskFinished = true;
    
    // Set default blackboard keys
    ReviveTargetKey.SelectedKeyName = FName("ReviveTarget");
    IsRevivingKey.SelectedKeyName = FName("IsReviving");
    IsUnderFireKey.SelectedKeyName = FName("IsUnderFire");
}

EBTNodeResult::Type UBTTask_ReviveAlly::ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    // Initialize task memory
    InitializeTaskMemory(NodeMemory);
    
    // Get AI Controller and validate
    AAIController* AIController = OwnerComp.GetAIOwner();
    if (!AIController)
    {
        UE_LOG(LogSquadMateAI, Warning, TEXT("BTTask_ReviveAlly: No AI Controller found"));
        return EBTNodeResult::Failed;
    }

    // Get blackboard component
    UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
    if (!BlackboardComp)
    {
        UE_LOG(LogSquadMateAI, Warning, TEXT("BTTask_ReviveAlly: No Blackboard Component found"));
        return EBTNodeResult::Failed;
    }

    // Get revive target from blackboard
    AActor* ReviveTarget = Cast<AActor>(BlackboardComp->GetValueAsObject(ReviveTargetKey.SelectedKeyName));
    if (!ReviveTarget)
    {
        UE_LOG(LogSquadMateAI, Warning, TEXT("BTTask_ReviveAlly: No valid revive target"));
        return EBTNodeResult::Failed;
    }

    // Validate revive target
    if (!ValidateReviveTarget(ReviveTarget, AIController->GetPawn()))
    {
        UE_LOG(LogSquadMateAI, Warning, TEXT("BTTask_ReviveAlly: Revive target validation failed"));
        return EBTNodeResult::Failed;
    }

    // Initialize revive process
    if (!InitializeRevive(OwnerComp, NodeMemory))
    {
        UE_LOG(LogSquadMateAI, Warning, TEXT("BTTask_ReviveAlly: Failed to initialize revive"));
        return EBTNodeResult::Failed;
    }

    // Set reviving state in blackboard
    SetRevivingState(OwnerComp, true);

    // Log revive start
    if (bLogReviveEvents)
    {
        LogReviveEvent(TEXT("Revive Started"), OwnerComp, ReviveTarget);
    }

    // Broadcast revive start
    if (bCallOutReviveStart)
    {
        BroadcastReviveStart(OwnerComp, ReviveTarget);
    }

    return EBTNodeResult::InProgress;
}

EBTNodeResult::Type UBTTask_ReviveAlly::AbortTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    // Handle revive interruption
    HandleReviveInterruption(OwnerComp, NodeMemory);
    
    // Complete revive with interrupted result
    CompleteRevive(OwnerComp, NodeMemory, EReviveResult::Interrupted);
    
    return EBTNodeResult::Aborted;
}

void UBTTask_ReviveAlly::TickTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaSeconds)
{
    FBTTask_ReviveAllyMemory* TaskMemory = GetTaskMemory(NodeMemory);
    if (!TaskMemory)
    {
        FinishLatentTask(OwnerComp, EBTNodeResult::Failed);
        return;
    }

    // Update revive progress
    UpdateRevive(OwnerComp, NodeMemory, DeltaSeconds);

    // Perform safety checks
    if (ShouldPerformExpensiveChecks(GetWorld()->GetTimeSeconds(), TaskMemory->LastSafetyCheck))
    {
        PerformSafetyCheck(OwnerComp, NodeMemory);
        TaskMemory->LastSafetyCheck = GetWorld()->GetTimeSeconds();
    }

    // Check if revive should be aborted
    if (ShouldAbortRevive(OwnerComp))
    {
        CompleteRevive(OwnerComp, NodeMemory, EReviveResult::Failed);
        FinishLatentTask(OwnerComp, EBTNodeResult::Failed);
        return;
    }

    // Check if revive is complete
    if (TaskMemory->ReviveProgress >= 1.0f)
    {
        CompleteRevive(OwnerComp, NodeMemory, EReviveResult::Success);
        FinishLatentTask(OwnerComp, EBTNodeResult::Succeeded);
        return;
    }

    // Draw debug info if enabled
    if (bDrawDebugInfo)
    {
        DrawDebugReviveInfo(GetWorld(), 
                           OwnerComp.GetAIOwner()->GetPawn()->GetActorLocation(),
                           TaskMemory->ReviveTarget.IsValid() ? TaskMemory->ReviveTarget->GetActorLocation() : FVector::ZeroVector,
                           EReviveResult::Success);
    }
}

void UBTTask_ReviveAlly::OnTaskFinished(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EBTNodeResult::Type TaskResult)
{
    // Set reviving state to false
    SetRevivingState(OwnerComp, false);
    
    // Cleanup task memory
    CleanupTaskMemory(NodeMemory);
    
    Super::OnTaskFinished(OwnerComp, NodeMemory, TaskResult);
}

FString UBTTask_ReviveAlly::GetStaticDescription() const
{
    return FString::Printf(TEXT("Revive Ally: %s\nMax Distance: %.1f\nRevive Time: %.1f"), 
                          *ReviveTargetKey.SelectedKeyName.ToString(),
                          MaxReviveDistance,
                          ReviveTime);
}

bool UBTTask_ReviveAlly::InitializeRevive(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    FBTTask_ReviveAllyMemory* TaskMemory = GetTaskMemory(NodeMemory);
    if (!TaskMemory)
    {
        return false;
    }

    // Get revive target
    UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
    AActor* ReviveTarget = Cast<AActor>(BlackboardComp->GetValueAsObject(ReviveTargetKey.SelectedKeyName));
    
    if (!ReviveTarget)
    {
        return false;
    }

    // Initialize task memory
    TaskMemory->ReviveTarget = ReviveTarget;
    TaskMemory->ReviveStartTime = GetWorld()->GetTimeSeconds();
    TaskMemory->ReviveProgress = 0.0f;
    TaskMemory->bReviveInProgress = true;
    TaskMemory->RevivePosition = ReviveTarget->GetActorLocation();

    // Start revive animation
    StartReviveAnimation(OwnerComp.GetAIOwner()->GetPawn());

    // Play revive effects
    PlayReviveEffects(TaskMemory->RevivePosition);

    return true;
}

void UBTTask_ReviveAlly::UpdateRevive(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime)
{
    FBTTask_ReviveAllyMemory* TaskMemory = GetTaskMemory(NodeMemory);
    if (!TaskMemory || !TaskMemory->bReviveInProgress)
    {
        return;
    }

    // Update progress based on time
    float ElapsedTime = GetWorld()->GetTimeSeconds() - TaskMemory->ReviveStartTime;
    TaskMemory->ReviveProgress = FMath::Clamp(ElapsedTime / ReviveTime, 0.0f, 1.0f);

    // Update revive progress on target
    UpdateReviveProgress(OwnerComp, DeltaTime);
}

void UBTTask_ReviveAlly::CompleteRevive(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EReviveResult Result)
{
    FBTTask_ReviveAllyMemory* TaskMemory = GetTaskMemory(NodeMemory);
    if (!TaskMemory)
    {
        return;
    }

    TaskMemory->bReviveInProgress = false;
    TaskMemory->LastResult = Result;

    // Stop animations and effects
    StopReviveAnimation(OwnerComp.GetAIOwner()->GetPawn());

    // Handle result
    switch (Result)
    {
        case EReviveResult::Success:
            if (TaskMemory->ReviveTarget.IsValid())
            {
                // Execute actual revive
                UReviveComponent* ReviveComp = GetReviveComponent(TaskMemory->ReviveTarget.Get());
                if (ReviveComp)
                {
                    ReviveComp->CompleteRevive();
                }

                // Broadcast success
                if (bCallOutReviveComplete)
                {
                    BroadcastReviveComplete(OwnerComp, TaskMemory->ReviveTarget.Get());
                }
            }
            break;

        case EReviveResult::Failed:
        case EReviveResult::Interrupted:
            // Broadcast failure
            if (TaskMemory->ReviveTarget.IsValid())
            {
                BroadcastReviveFailed(OwnerComp, TaskMemory->ReviveTarget.Get(), Result);
            }
            break;
    }

    // Log result
    if (bLogReviveEvents)
    {
        FString ResultString = UEnum::GetValueAsString(Result);
        LogReviveEvent(FString::Printf(TEXT("Revive Completed: %s"), *ResultString), OwnerComp, TaskMemory->ReviveTarget.Get());
    }
}

bool UBTTask_ReviveAlly::ValidateReviveTarget(AActor* Target, AActor* Reviver)
{
    if (!Target || !Reviver)
    {
        return false;
    }

    // Check distance
    if (!IsWithinReviveRange(Reviver, Target))
    {
        return false;
    }

    // Check if target needs revival
    UHealthComponent* HealthComp = GetHealthComponent(Target);
    if (!HealthComp || !HealthComp->IsDowned())
    {
        return false;
    }

    // Check line of sight if required
    if (bRequireLineOfSight && !HasLineOfSightToTarget(Reviver, Target))
    {
        return false;
    }

    return true;
}

bool UBTTask_ReviveAlly::IsWithinReviveRange(AActor* Reviver, AActor* Target)
{
    if (!Reviver || !Target)
    {
        return false;
    }

    float Distance = GetDistanceToTarget(Reviver, Target);
    return Distance <= MaxReviveDistance;
}

float UBTTask_ReviveAlly::GetDistanceToTarget(AActor* From, AActor* To)
{
    if (!From || !To)
    {
        return FLT_MAX;
    }

    return FVector::Dist(From->GetActorLocation(), To->GetActorLocation());
}

bool UBTTask_ReviveAlly::HasLineOfSightToTarget(AActor* From, AActor* To)
{
    if (!From || !To)
    {
        return false;
    }

    FHitResult HitResult;
    FVector Start = From->GetActorLocation();
    FVector End = To->GetActorLocation();

    bool bHit = GetWorld()->LineTraceSingleByChannel(
        HitResult,
        Start,
        End,
        ECC_Visibility
    );

    return !bHit || HitResult.GetActor() == To;
}

void UBTTask_ReviveAlly::InitializeTaskMemory(uint8* NodeMemory)
{
    FBTTask_ReviveAllyMemory* TaskMemory = new(NodeMemory) FBTTask_ReviveAllyMemory();
}

void UBTTask_ReviveAlly::CleanupTaskMemory(uint8* NodeMemory)
{
    FBTTask_ReviveAllyMemory* TaskMemory = GetTaskMemory(NodeMemory);
    if (TaskMemory)
    {
        TaskMemory->~FBTTask_ReviveAllyMemory();
    }
}

UBTTask_ReviveAlly::FBTTask_ReviveAllyMemory* UBTTask_ReviveAlly::GetTaskMemory(uint8* NodeMemory)
{
    return reinterpret_cast<FBTTask_ReviveAllyMemory*>(NodeMemory);
}

uint16 UBTTask_ReviveAlly::GetInstanceMemorySize() const
{
    return sizeof(FBTTask_ReviveAllyMemory);
}

// Static utility functions
bool UBTTask_ReviveAlly::CanReviveTarget(AActor* Reviver, AActor* Target, float MaxDistance)
{
    if (!Reviver || !Target)
    {
        return false;
    }

    // Check distance
    float Distance = FVector::Dist(Reviver->GetActorLocation(), Target->GetActorLocation());
    if (Distance > MaxDistance)
    {
        return false;
    }

    // Check if target is downed
    if (UHealthComponent* HealthComp = Target->FindComponentByClass<UHealthComponent>())
    {
        return HealthComp->IsDowned();
    }

    return false;
}

bool UBTTask_ReviveAlly::IsReviveSafe(AActor* Reviver, AActor* Target, float SafetyRadius)
{
    if (!Reviver || !Target)
    {
        return false;
    }

    // Simple safety check - can be expanded
    return true;
}

float UBTTask_ReviveAlly::GetReviveProgress(AActor* Target)
{
    if (!Target)
    {
        return 0.0f;
    }

    if (UReviveComponent* ReviveComp = Target->FindComponentByClass<UReviveComponent>())
    {
        return ReviveComp->GetReviveProgress();
    }

    return 0.0f;
}

// Additional implementation methods
void UBTTask_ReviveAlly::StartReviveAnimation(AActor* Reviver)
{
    if (!Reviver)
    {
        return;
    }

    if (ACharacter* Character = Cast<ACharacter>(Reviver))
    {
        if (ReviveMontage)
        {
            Character->PlayAnimMontage(ReviveMontage);
        }
    }
}

void UBTTask_ReviveAlly::StopReviveAnimation(AActor* Reviver)
{
    if (!Reviver)
    {
        return;
    }

    if (ACharacter* Character = Cast<ACharacter>(Reviver))
    {
        if (ReviveMontage)
        {
            Character->StopAnimMontage(ReviveMontage);
        }
    }
}

void UBTTask_ReviveAlly::PlayReviveEffects(const FVector& Location)
{
    if (ReviveEffect && GetWorld())
    {
        // Spawn particle effect
        // UGameplayStatics::SpawnEmitterAtLocation(GetWorld(), ReviveEffect, Location);
    }

    if (ReviveSound && GetWorld())
    {
        // Play sound effect
        // UGameplayStatics::PlaySoundAtLocation(GetWorld(), ReviveSound, Location);
    }
}

void UBTTask_ReviveAlly::UpdateReviveProgress(UBehaviorTreeComponent& OwnerComp, float DeltaTime)
{
    FBTTask_ReviveAllyMemory* TaskMemory = GetTaskMemory(OwnerComp.GetNodeMemory(this));
    if (!TaskMemory || !TaskMemory->ReviveTarget.IsValid())
    {
        return;
    }

    // Update revive component progress
    if (UReviveComponent* ReviveComp = GetReviveComponent(TaskMemory->ReviveTarget.Get()))
    {
        ReviveComp->UpdateReviveProgress(TaskMemory->ReviveProgress);
    }
}

void UBTTask_ReviveAlly::BroadcastReviveStart(UBehaviorTreeComponent& OwnerComp, AActor* Target)
{
    if (ASquadMateAIController* AIController = GetSquadMateController(OwnerComp))
    {
        AIController->BroadcastToSquad(ReviveStartCallout, 1.0f);
    }
}

void UBTTask_ReviveAlly::BroadcastReviveComplete(UBehaviorTreeComponent& OwnerComp, AActor* Target)
{
    if (ASquadMateAIController* AIController = GetSquadMateController(OwnerComp))
    {
        AIController->BroadcastToSquad(ReviveCompleteCallout, 1.0f);
    }
}

void UBTTask_ReviveAlly::BroadcastReviveFailed(UBehaviorTreeComponent& OwnerComp, AActor* Target, EReviveResult Reason)
{
    if (ASquadMateAIController* AIController = GetSquadMateController(OwnerComp))
    {
        FString FailureMessage = FString::Printf(TEXT("Revive failed: %s"), *UEnum::GetValueAsString(Reason));
        AIController->BroadcastToSquad(FailureMessage, 0.8f);
    }
}

void UBTTask_ReviveAlly::PerformSafetyCheck(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    if (bCheckForEnemies)
    {
        FBTTask_ReviveAllyMemory* TaskMemory = GetTaskMemory(NodeMemory);
        if (TaskMemory)
        {
            TArray<AActor*> NearbyEnemies = GetNearbyEnemies(TaskMemory->RevivePosition, EnemyCheckRadius);
            if (NearbyEnemies.Num() > 0 && bAbortIfUnderFire)
            {
                // Set under fire flag
                if (UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent())
                {
                    BlackboardComp->SetValueAsBool(IsUnderFireKey.SelectedKeyName, true);
                }
            }
        }
    }
}

bool UBTTask_ReviveAlly::ShouldAbortRevive(UBehaviorTreeComponent& OwnerComp)
{
    if (bAbortIfUnderFire)
    {
        if (UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent())
        {
            return BlackboardComp->GetValueAsBool(IsUnderFireKey.SelectedKeyName);
        }
    }
    return false;
}

void UBTTask_ReviveAlly::HandleReviveInterruption(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    // Stop animations and effects
    if (AAIController* AIController = OwnerComp.GetAIOwner())
    {
        StopReviveAnimation(AIController->GetPawn());
    }
}

TArray<AActor*> UBTTask_ReviveAlly::GetNearbyEnemies(const FVector& Location, float Radius)
{
    TArray<AActor*> NearbyEnemies;

    // Simple implementation - can be expanded with proper enemy detection
    if (GetWorld())
    {
        TArray<FOverlapResult> OverlapResults;
        FCollisionQueryParams QueryParams;
        QueryParams.bTraceComplex = false;

        if (GetWorld()->OverlapMultiByChannel(OverlapResults, Location, FQuat::Identity,
                                            ECC_Pawn, FCollisionShape::MakeSphere(Radius), QueryParams))
        {
            for (const FOverlapResult& Result : OverlapResults)
            {
                if (AActor* Actor = Result.GetActor())
                {
                    // Add enemy detection logic here
                    NearbyEnemies.Add(Actor);
                }
            }
        }
    }

    return NearbyEnemies;
}

UHealthComponent* UBTTask_ReviveAlly::GetHealthComponent(AActor* Actor)
{
    if (!Actor)
    {
        return nullptr;
    }
    return Actor->FindComponentByClass<UHealthComponent>();
}

UReviveComponent* UBTTask_ReviveAlly::GetReviveComponent(AActor* Actor)
{
    if (!Actor)
    {
        return nullptr;
    }
    return Actor->FindComponentByClass<UReviveComponent>();
}

ASquadMateAIController* UBTTask_ReviveAlly::GetSquadMateController(UBehaviorTreeComponent& OwnerComp)
{
    return Cast<ASquadMateAIController>(OwnerComp.GetAIOwner());
}

void UBTTask_ReviveAlly::SetRevivingState(UBehaviorTreeComponent& OwnerComp, bool bIsReviving)
{
    if (UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent())
    {
        BlackboardComp->SetValueAsBool(IsRevivingKey.SelectedKeyName, bIsReviving);
    }
}

void UBTTask_ReviveAlly::LogReviveEvent(const FString& Event, UBehaviorTreeComponent& OwnerComp, AActor* Target)
{
    FString TargetName = Target ? Target->GetName() : TEXT("None");
    UE_LOG(LogSquadMateAI, Log, TEXT("BTTask_ReviveAlly: %s - Target: %s"), *Event, *TargetName);
}

void UBTTask_ReviveAlly::DrawDebugReviveInfo(UWorld* World, const FVector& ReviverLocation, const FVector& TargetLocation, EReviveResult Status)
{
    if (!World)
    {
        return;
    }

    // Draw line between reviver and target
    DrawDebugLine(World, ReviverLocation, TargetLocation, FColor::Green, false, 0.1f, 0, 2.0f);

    // Draw sphere at target location
    DrawDebugSphere(World, TargetLocation, 50.0f, 12, FColor::Yellow, false, 0.1f);

    // Draw revive radius
    DrawDebugSphere(World, TargetLocation, MaxReviveDistance, 16, FColor::Blue, false, 0.1f);
}

bool UBTTask_ReviveAlly::ShouldPerformExpensiveChecks(float CurrentTime, float LastCheckTime)
{
    return (CurrentTime - LastCheckTime) >= SafetyCheckInterval;
}
