# 🗺️ Map Positions DataTable Setup Guide

## 📋 Create DT_MapPositions DataTable

### Step 1: Create the DataTable
1. **Right-click in Content/Data/DataTables/**
2. **Miscellaneous → Data Table**
3. **Choose Row Structure: `DataTableRowBase`**
4. **Name: `DT_MapPositions`**

### Step 2: Add Columns
Add these columns to your DataTable:

| Column Name | Type | Description |
|-------------|------|-------------|
| PositionName | String | Unique position identifier |
| PositionType | String | Type of position (Spawn, Strategic, Cover, etc.) |
| LocationX | Float | X coordinate |
| LocationY | Float | Y coordinate |
| LocationZ | Float | Z coordinate |
| RotationYaw | Float | Facing direction (degrees) |
| TeamID | Integer | Team ownership (0=Blue, 1=Red, -1=Neutral) |
| LaneType | String | Lane assignment (Left, Center, Right) |
| Importance | String | Strategic importance (High, Medium, Low) |
| PreferredRoles | String | Comma-separated preferred roles |
| CoverType | String | Type of cover available |
| SightLines | String | Comma-separated visible positions |
| Radius | Float | Area of influence |
| IsSpawnPoint | Boolean | Is this a spawn location |
| IsObjective | Boolean | Is this an objective point |

### Step 3: Populate with TDM Warehouse Positions

#### Team 0 (Blue) Spawn Points
**Row: Blue_Spawn_01**
- PositionName: "Blue_Spawn_01"
- PositionType: "SpawnPoint"
- LocationX: -800.0
- LocationY: -200.0
- LocationZ: 100.0
- RotationYaw: 0.0
- TeamID: 0
- LaneType: "Spawn"
- Importance: "High"
- PreferredRoles: "Any"
- CoverType: "Full"
- SightLines: "Blue_Forward_01,Center_Control"
- Radius: 200.0
- IsSpawnPoint: true
- IsObjective: false

**Row: Blue_Spawn_02**
- PositionName: "Blue_Spawn_02"
- PositionType: "SpawnPoint"
- LocationX: -800.0
- LocationY: 0.0
- LocationZ: 100.0
- RotationYaw: 0.0
- TeamID: 0
- LaneType: "Spawn"
- Importance: "High"
- PreferredRoles: "Any"
- CoverType: "Full"
- SightLines: "Blue_Forward_02,Center_Control"
- Radius: 200.0
- IsSpawnPoint: true
- IsObjective: false

**Row: Blue_Spawn_03**
- PositionName: "Blue_Spawn_03"
- PositionType: "SpawnPoint"
- LocationX: -800.0
- LocationY: 200.0
- LocationZ: 100.0
- RotationYaw: 0.0
- TeamID: 0
- LaneType: "Spawn"
- Importance: "High"
- PreferredRoles: "Any"
- CoverType: "Full"
- SightLines: "Blue_Forward_03,Center_Control"
- Radius: 200.0
- IsSpawnPoint: true
- IsObjective: false

**Row: Blue_Spawn_04**
- PositionName: "Blue_Spawn_04"
- PositionType: "SpawnPoint"
- LocationX: -850.0
- LocationY: -100.0
- LocationZ: 100.0
- RotationYaw: 0.0
- TeamID: 0
- LaneType: "Spawn"
- Importance: "High"
- PreferredRoles: "Any"
- CoverType: "Full"
- SightLines: "Blue_Forward_04,Left_Lane"
- Radius: 200.0
- IsSpawnPoint: true
- IsObjective: false

**Row: Blue_Spawn_05**
- PositionName: "Blue_Spawn_05"
- PositionType: "SpawnPoint"
- LocationX: -850.0
- LocationY: 100.0
- LocationZ: 100.0
- RotationYaw: 0.0
- TeamID: 0
- LaneType: "Spawn"
- Importance: "High"
- PreferredRoles: "Any"
- CoverType: "Full"
- SightLines: "Blue_Forward_05,Right_Lane"
- Radius: 200.0
- IsSpawnPoint: true
- IsObjective: false

#### Team 1 (Red) Spawn Points
**Row: Red_Spawn_01**
- PositionName: "Red_Spawn_01"
- PositionType: "SpawnPoint"
- LocationX: 800.0
- LocationY: -200.0
- LocationZ: 100.0
- RotationYaw: 180.0
- TeamID: 1
- LaneType: "Spawn"
- Importance: "High"
- PreferredRoles: "Any"
- CoverType: "Full"
- SightLines: "Red_Forward_01,Center_Control"
- Radius: 200.0
- IsSpawnPoint: true
- IsObjective: false

**Row: Red_Spawn_02**
- PositionName: "Red_Spawn_02"
- PositionType: "SpawnPoint"
- LocationX: 800.0
- LocationY: 0.0
- LocationZ: 100.0
- RotationYaw: 180.0
- TeamID: 1
- LaneType: "Spawn"
- Importance: "High"
- PreferredRoles: "Any"
- CoverType: "Full"
- SightLines: "Red_Forward_02,Center_Control"
- Radius: 200.0
- IsSpawnPoint: true
- IsObjective: false

**Row: Red_Spawn_03**
- PositionName: "Red_Spawn_03"
- PositionType: "SpawnPoint"
- LocationX: 800.0
- LocationY: 200.0
- LocationZ: 100.0
- RotationYaw: 180.0
- TeamID: 1
- LaneType: "Spawn"
- Importance: "High"
- PreferredRoles: "Any"
- CoverType: "Full"
- SightLines: "Red_Forward_03,Center_Control"
- Radius: 200.0
- IsSpawnPoint: true
- IsObjective: false

**Row: Red_Spawn_04**
- PositionName: "Red_Spawn_04"
- PositionType: "SpawnPoint"
- LocationX: 850.0
- LocationY: -100.0
- LocationZ: 100.0
- RotationYaw: 180.0
- TeamID: 1
- LaneType: "Spawn"
- Importance: "High"
- PreferredRoles: "Any"
- CoverType: "Full"
- SightLines: "Red_Forward_04,Left_Lane"
- Radius: 200.0
- IsSpawnPoint: true
- IsObjective: false

**Row: Red_Spawn_05**
- PositionName: "Red_Spawn_05"
- PositionType: "SpawnPoint"
- LocationX: 850.0
- LocationY: 100.0
- LocationZ: 100.0
- RotationYaw: 180.0
- TeamID: 1
- LaneType: "Spawn"
- Importance: "High"
- PreferredRoles: "Any"
- CoverType: "Full"
- SightLines: "Red_Forward_05,Right_Lane"
- Radius: 200.0
- IsSpawnPoint: true
- IsObjective: false

#### Strategic Positions

#### Center Control (Key Objective)
**Row: Center_Control**
- PositionName: "Center_Control"
- PositionType: "Strategic"
- LocationX: 0.0
- LocationY: 0.0
- LocationZ: 100.0
- RotationYaw: 0.0
- TeamID: -1
- LaneType: "Center"
- Importance: "High"
- PreferredRoles: "Assault,Support"
- CoverType: "Partial"
- SightLines: "Left_Lane,Right_Lane,Blue_Forward,Red_Forward"
- Radius: 150.0
- IsSpawnPoint: false
- IsObjective: true

#### Left Lane Positions
**Row: Left_Lane_Forward**
- PositionName: "Left_Lane_Forward"
- PositionType: "Strategic"
- LocationX: -300.0
- LocationY: -400.0
- LocationZ: 100.0
- RotationYaw: 45.0
- TeamID: -1
- LaneType: "Left"
- Importance: "Medium"
- PreferredRoles: "Flanker,Entry"
- CoverType: "Partial"
- SightLines: "Center_Control,Left_Flank"
- Radius: 100.0
- IsSpawnPoint: false
- IsObjective: false

**Row: Left_Flank**
- PositionName: "Left_Flank"
- PositionType: "Flanking"
- LocationX: -500.0
- LocationY: -200.0
- LocationZ: 100.0
- RotationYaw: 90.0
- TeamID: -1
- LaneType: "Left"
- Importance: "Medium"
- PreferredRoles: "Flanker"
- CoverType: "Good"
- SightLines: "Center_Control,Right_Lane"
- Radius: 80.0
- IsSpawnPoint: false
- IsObjective: false

#### Right Lane Positions
**Row: Right_Lane_Forward**
- PositionName: "Right_Lane_Forward"
- PositionType: "Strategic"
- LocationX: 300.0
- LocationY: 400.0
- LocationZ: 100.0
- RotationYaw: -45.0
- TeamID: -1
- LaneType: "Right"
- Importance: "Medium"
- PreferredRoles: "Flanker,Entry"
- CoverType: "Partial"
- SightLines: "Center_Control,Right_Flank"
- Radius: 100.0
- IsSpawnPoint: false
- IsObjective: false

**Row: Right_Flank**
- PositionName: "Right_Flank"
- PositionType: "Flanking"
- LocationX: 500.0
- LocationY: 200.0
- LocationZ: 100.0
- RotationYaw: -90.0
- TeamID: -1
- LaneType: "Right"
- Importance: "Medium"
- PreferredRoles: "Flanker"
- CoverType: "Good"
- SightLines: "Center_Control,Left_Lane"
- Radius: 80.0
- IsSpawnPoint: false
- IsObjective: false

#### Sniper Positions
**Row: Blue_Sniper_Overwatch**
- PositionName: "Blue_Sniper_Overwatch"
- PositionType: "Overwatch"
- LocationX: -400.0
- LocationY: 0.0
- LocationZ: 150.0
- RotationYaw: 0.0
- TeamID: 0
- LaneType: "Center"
- Importance: "High"
- PreferredRoles: "Sniper"
- CoverType: "Excellent"
- SightLines: "Center_Control,Left_Lane,Right_Lane,Red_Forward"
- Radius: 50.0
- IsSpawnPoint: false
- IsObjective: false

**Row: Red_Sniper_Overwatch**
- PositionName: "Red_Sniper_Overwatch"
- PositionType: "Overwatch"
- LocationX: 400.0
- LocationY: 0.0
- LocationZ: 150.0
- RotationYaw: 180.0
- TeamID: 1
- LaneType: "Center"
- Importance: "High"
- PreferredRoles: "Sniper"
- CoverType: "Excellent"
- SightLines: "Center_Control,Left_Lane,Right_Lane,Blue_Forward"
- Radius: 50.0
- IsSpawnPoint: false
- IsObjective: false

#### Cover Positions
**Row: Center_Cover_01**
- PositionName: "Center_Cover_01"
- PositionType: "Cover"
- LocationX: -100.0
- LocationY: -50.0
- LocationZ: 100.0
- RotationYaw: 45.0
- TeamID: -1
- LaneType: "Center"
- Importance: "Medium"
- PreferredRoles: "Any"
- CoverType: "Good"
- SightLines: "Center_Control,Left_Lane"
- Radius: 30.0
- IsSpawnPoint: false
- IsObjective: false

**Row: Center_Cover_02**
- PositionName: "Center_Cover_02"
- PositionType: "Cover"
- LocationX: 100.0
- LocationY: 50.0
- LocationZ: 100.0
- RotationYaw: -135.0
- TeamID: -1
- LaneType: "Center"
- Importance: "Medium"
- PreferredRoles: "Any"
- CoverType: "Good"
- SightLines: "Center_Control,Right_Lane"
- Radius: 30.0
- IsSpawnPoint: false
- IsObjective: false

### Step 4: Usage in AI System

```blueprint
// Get spawn position for team
Get Data Table Rows
├── Data Table: DT_MapPositions
├── Filter: IsSpawnPoint = true AND TeamID = [TeamID]
└── Return: Random spawn position

// Get strategic position for role
Get Data Table Rows
├── Data Table: DT_MapPositions
├── Filter: PositionType = "Strategic" AND PreferredRoles contains [Role]
└── Return: Best strategic position

// Find cover position
Get Data Table Rows
├── Data Table: DT_MapPositions
├── Filter: PositionType = "Cover" AND Distance < [MaxDistance]
└── Return: Nearest cover position
```

This DataTable provides complete map positioning data for authentic PUBGM TDM warehouse layout and tactical positioning.
