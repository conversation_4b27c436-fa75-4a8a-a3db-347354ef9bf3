#include "AI/SquadMateAIController.h"
#include "AI/SquadManager.h"
#include "AI/SquadMateCharacter.h"
#include "Components/DecisionLoggerComponent.h"
#include "Components/HealthComponent.h"
#include "Components/InventoryComponent.h"
#include "Components/SquadRoleComponent.h"
#include "BehaviorTree/BehaviorTree.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "Perception/AIPerceptionComponent.h"
#include "Perception/AISenseConfig_Sight.h"
#include "Perception/AISenseConfig_Hearing.h"
#include "GameFramework/Character.h"
#include "Engine/World.h"
#include "TimerManager.h"

ASquadMateAIController::ASquadMateAIController()
{
    PrimaryActorTick.bCanEverTick = true;

    // Initialize AI Perception
    AIPerceptionComponent = CreateDefaultSubobject<UAIPerceptionComponent>(TEXT("AIPerceptionComponent"));
    
    // Initialize Behavior Tree Component
    BehaviorTreeComponent = CreateDefaultSubobject<UBehaviorTreeComponent>(TEXT("BehaviorTreeComponent"));
    
    // Initialize Blackboard Component
    BlackboardComponent = CreateDefaultSubobject<UBlackboardComponent>(TEXT("BlackboardComponent"));
    
    // Initialize Decision Logger
    DecisionLogger = CreateDefaultSubobject<UDecisionLoggerComponent>(TEXT("DecisionLogger"));

    // Set default values
    CurrentTactic = ETacticState::Patrol;
    AssignedRole = ESquadRole::Assault;
    LastCombatTime = 0.0f;
    LastDecisionTime = 0.0f;
}

void ASquadMateAIController::BeginPlay()
{
    Super::BeginPlay();
    
    InitializeAIPerception();
    InitializeBehaviorTree();
    
    // Set up decision making timer
    GetWorld()->GetTimerManager().SetTimer(
        FTimerHandle(),
        this,
        &ASquadMateAIController::MakeDecision,
        0.5f, // Decision frequency
        true
    );
}

void ASquadMateAIController::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    UpdateBlackboardValues();
    EvaluateTacticalSituation();
}

void ASquadMateAIController::OnPossess(APawn* InPawn)
{
    Super::OnPossess(InPawn);
    
    if (BehaviorTree && BlackboardComponent)
    {
        BlackboardComponent->InitializeBlackboard(*BehaviorTree->BlackboardAsset);
        BehaviorTreeComponent->StartTree(*BehaviorTree);
    }
}

void ASquadMateAIController::OnUnPossess()
{
    if (BehaviorTreeComponent)
    {
        BehaviorTreeComponent->StopTree();
    }
    
    Super::OnUnPossess();
}

void ASquadMateAIController::InitializeAIPerception()
{
    if (!AIPerceptionComponent)
        return;

    // Configure Sight
    SightConfig = CreateDefaultSubobject<UAISenseConfig_Sight>(TEXT("SightConfig"));
    SightConfig->SightRadius = 2000.0f;
    SightConfig->LoseSightRadius = 2200.0f;
    SightConfig->PeripheralVisionAngleDegrees = 90.0f;
    SightConfig->SetMaxAge(5.0f);
    SightConfig->AutoSuccessRangeFromLastSeenLocation = 500.0f;
    SightConfig->DetectionByAffiliation.bDetectNeutrals = true;
    SightConfig->DetectionByAffiliation.bDetectFriendlies = true;
    SightConfig->DetectionByAffiliation.bDetectEnemies = true;

    // Configure Hearing
    HearingConfig = CreateDefaultSubobject<UAISenseConfig_Hearing>(TEXT("HearingConfig"));
    HearingConfig->HearingRange = 1500.0f;
    HearingConfig->SetMaxAge(3.0f);
    HearingConfig->DetectionByAffiliation.bDetectNeutrals = true;
    HearingConfig->DetectionByAffiliation.bDetectFriendlies = true;
    HearingConfig->DetectionByAffiliation.bDetectEnemies = true;

    // Add senses to perception component
    AIPerceptionComponent->ConfigureSense(*SightConfig);
    AIPerceptionComponent->ConfigureSense(*HearingConfig);
    AIPerceptionComponent->SetDominantSense(SightConfig->GetSenseImplementation());

    // Bind perception events
    AIPerceptionComponent->OnPerceptionUpdated.AddDynamic(this, &ASquadMateAIController::OnPerceptionUpdated);
    AIPerceptionComponent->OnTargetPerceptionUpdated.AddDynamic(this, &ASquadMateAIController::OnTargetPerceptionUpdated);
}

void ASquadMateAIController::InitializeBehaviorTree()
{
    if (BehaviorTree && BlackboardComponent)
    {
        BlackboardComponent->InitializeBlackboard(*BehaviorTree->BlackboardAsset);
        
        // Set initial blackboard values
        BlackboardComponent->SetValueAsEnum(TacticStateKey, static_cast<uint8>(CurrentTactic));
        BlackboardComponent->SetValueAsEnum(SquadRoleKey, static_cast<uint8>(AssignedRole));
        BlackboardComponent->SetValueAsBool(IsInCombatKey, false);
    }
}

void ASquadMateAIController::UpdateBlackboardValues()
{
    if (!BlackboardComponent || !GetPawn())
        return;

    // Update health percentage
    if (UHealthComponent* HealthComp = GetPawn()->FindComponentByClass<UHealthComponent>())
    {
        float HealthPercent = HealthComp->GetHealthPercentage();
        BlackboardComponent->SetValueAsFloat(HealthPercentageKey, HealthPercent);
    }

    // Update ammo count
    if (UInventoryComponent* InventoryComp = GetPawn()->FindComponentByClass<UInventoryComponent>())
    {
        int32 AmmoCount = InventoryComp->GetCurrentAmmo();
        BlackboardComponent->SetValueAsInt(AmmoCountKey, AmmoCount);
    }

    // Update combat state
    bool bInCombat = (GetWorld()->GetTimeSeconds() - LastCombatTime) < 5.0f;
    BlackboardComponent->SetValueAsBool(IsInCombatKey, bInCombat);

    // Update tactic state
    BlackboardComponent->SetValueAsEnum(TacticStateKey, static_cast<uint8>(CurrentTactic));
}

void ASquadMateAIController::OnPerceptionUpdated(const TArray<AActor*>& UpdatedActors)
{
    for (AActor* Actor : UpdatedActors)
    {
        if (!Actor)
            continue;

        // Check if it's an enemy or ally
        if (ASquadMateCharacter* Character = Cast<ASquadMateCharacter>(Actor))
        {
            if (Character->GetTeamID() != Cast<ASquadMateCharacter>(GetPawn())->GetTeamID())
            {
                ProcessEnemyDetection(Actor);
            }
            else
            {
                ProcessAllyDetection(Actor);
            }
        }
    }
}

void ASquadMateAIController::OnTargetPerceptionUpdated(AActor* Actor, FAIStimulus Stimulus)
{
    if (!Actor)
        return;

    if (Stimulus.WasSuccessfullySensed())
    {
        // Target detected
        if (ASquadMateCharacter* Character = Cast<ASquadMateCharacter>(Actor))
        {
            if (Character->GetTeamID() != Cast<ASquadMateCharacter>(GetPawn())->GetTeamID())
            {
                // Enemy detected
                BlackboardComponent->SetValueAsObject(TargetActorKey, Actor);
                BlackboardComponent->SetValueAsVector(LastKnownEnemyLocationKey, Actor->GetActorLocation());
                LastCombatTime = GetWorld()->GetTimeSeconds();
                
                // Log decision
                if (DecisionLogger)
                {
                    DecisionLogger->LogDecision(TEXT("EnemyDetected"), Actor->GetActorLocation(), true);
                }
            }
        }
    }
    else
    {
        // Target lost
        if (BlackboardComponent->GetValueAsObject(TargetActorKey) == Actor)
        {
            BlackboardComponent->ClearValue(TargetActorKey);
        }
    }
}

void ASquadMateAIController::ProcessEnemyDetection(AActor* Enemy)
{
    if (!DetectedEnemies.Contains(Enemy))
    {
        DetectedEnemies.Add(Enemy);
    }

    // Update blackboard with closest enemy
    AActor* ClosestEnemy = nullptr;
    float ClosestDistance = FLT_MAX;

    for (AActor* DetectedEnemy : DetectedEnemies)
    {
        if (IsValid(DetectedEnemy))
        {
            float Distance = GetDistanceToTarget(DetectedEnemy);
            if (Distance < ClosestDistance)
            {
                ClosestDistance = Distance;
                ClosestEnemy = DetectedEnemy;
            }
        }
    }

    if (ClosestEnemy)
    {
        BlackboardComponent->SetValueAsObject(TargetActorKey, ClosestEnemy);
        BlackboardComponent->SetValueAsVector(LastKnownEnemyLocationKey, ClosestEnemy->GetActorLocation());
    }
}

void ASquadMateAIController::ProcessAllyDetection(AActor* Ally)
{
    if (!DetectedAllies.Contains(Ally))
    {
        DetectedAllies.Add(Ally);
    }

    // Check if ally needs revival
    if (UHealthComponent* AllyHealth = Ally->FindComponentByClass<UHealthComponent>())
    {
        if (AllyHealth->IsDown() && !AllyHealth->IsDead())
        {
            BlackboardComponent->SetValueAsObject(AllyToReviveKey, Ally);
        }
    }
}

void ASquadMateAIController::EvaluateTacticalSituation()
{
    // Clean up invalid actors
    DetectedEnemies.RemoveAll([](AActor* Actor) { return !IsValid(Actor); });
    DetectedAllies.RemoveAll([](AActor* Actor) { return !IsValid(Actor); });
}

void ASquadMateAIController::MakeDecision()
{
    ETacticState NewTactic = DetermineBestTactic();
    
    if (NewTactic != CurrentTactic)
    {
        SetTacticState(NewTactic);
        ExecuteTactic(NewTactic);
        
        if (DecisionLogger)
        {
            FString TacticName = UEnum::GetValueAsString(NewTactic);
            DecisionLogger->LogDecision(TacticName, GetPawn()->GetActorLocation(), true);
        }
    }
    
    LastDecisionTime = GetWorld()->GetTimeSeconds();
}

ETacticState ASquadMateAIController::DetermineBestTactic()
{
    // Priority-based decision making
    
    // 1. Check if we should revive an ally
    if (ShouldReviveAlly())
    {
        return ETacticState::Revive;
    }
    
    // 2. Check if we should retreat
    if (ShouldRetreat())
    {
        return ETacticState::Retreat;
    }
    
    // 3. Check if we have a target to engage
    if (BlackboardComponent->GetValueAsObject(TargetActorKey))
    {
        if (ShouldFlank())
        {
            return ETacticState::Flank;
        }
        else if (CanEngageTarget(BlackboardComponent->GetValueAsObject(TargetActorKey)))
        {
            return ETacticState::Engage;
        }
        else if (ShouldSeekCover())
        {
            return ETacticState::Hold;
        }
    }
    
    // 4. Default to patrol
    return ETacticState::Patrol;
}

void ASquadMateAIController::ExecuteTactic(ETacticState Tactic)
{
    // The actual execution is handled by the Behavior Tree
    // This method can be used for immediate actions or state changes
    
    switch (Tactic)
    {
        case ETacticState::Engage:
            // Set aggressive behavior parameters
            break;
        case ETacticState::Retreat:
            // Set defensive behavior parameters
            break;
        case ETacticState::Flank:
            // Set flanking behavior parameters
            break;
        default:
            break;
    }
}

// Public Interface Implementation
void ASquadMateAIController::SetTacticState(ETacticState NewState)
{
    CurrentTactic = NewState;
    if (BlackboardComponent)
    {
        BlackboardComponent->SetValueAsEnum(TacticStateKey, static_cast<uint8>(NewState));
    }
}

void ASquadMateAIController::SetSquadRole(ESquadRole NewRole)
{
    AssignedRole = NewRole;
    if (BlackboardComponent)
    {
        BlackboardComponent->SetValueAsEnum(SquadRoleKey, static_cast<uint8>(NewRole));
    }
}

void ASquadMateAIController::RegisterWithSquad(ASquadManager* Manager)
{
    SquadManager = Manager;
    if (SquadManager)
    {
        SquadManager->RegisterAgent(this);
    }
}

void ASquadMateAIController::AddKill()
{
    AgentStats.Kills++;
    AgentStats.ELOScore += 25.0f;
}

void ASquadMateAIController::AddDeath()
{
    AgentStats.Deaths++;
    AgentStats.ELOScore -= 15.0f;
}

void ASquadMateAIController::AddRevive()
{
    AgentStats.Revives++;
    AgentStats.ELOScore += 10.0f;
}

void ASquadMateAIController::AddFlankSuccess()
{
    AgentStats.FlankSuccesses++;
    AgentStats.ELOScore += 15.0f;
}

// Utility Functions
float ASquadMateAIController::GetDistanceToTarget(AActor* Target) const
{
    if (!Target || !GetPawn())
        return FLT_MAX;
    
    return FVector::Dist(GetPawn()->GetActorLocation(), Target->GetActorLocation());
}

bool ASquadMateAIController::IsInLineOfSight(AActor* Target) const
{
    if (!Target || !GetPawn())
        return false;
    
    FHitResult HitResult;
    FVector Start = GetPawn()->GetActorLocation();
    FVector End = Target->GetActorLocation();
    
    bool bHit = GetWorld()->LineTraceSingleByChannel(
        HitResult,
        Start,
        End,
        ECollisionChannel::ECC_Visibility
    );
    
    return !bHit || HitResult.GetActor() == Target;
}

bool ASquadMateAIController::HasClearShotToTarget(AActor* Target) const
{
    return IsInLineOfSight(Target) && GetDistanceToTarget(Target) < 1500.0f;
}

bool ASquadMateAIController::CanEngageTarget(AActor* Target) const
{
    if (!Target)
        return false;
    
    // Check if we have ammo
    if (UInventoryComponent* InventoryComp = GetPawn()->FindComponentByClass<UInventoryComponent>())
    {
        if (InventoryComp->GetCurrentAmmo() <= 0)
            return false;
    }
    
    // Check if we have enough health
    if (UHealthComponent* HealthComp = GetPawn()->FindComponentByClass<UHealthComponent>())
    {
        if (HealthComp->GetHealthPercentage() < 0.3f)
            return false;
    }
    
    return HasClearShotToTarget(Target);
}

bool ASquadMateAIController::ShouldRetreat() const
{
    // Check health
    if (UHealthComponent* HealthComp = GetPawn()->FindComponentByClass<UHealthComponent>())
    {
        if (HealthComp->GetHealthPercentage() < 0.25f)
            return true;
    }
    
    // Check if outnumbered
    int32 NearbyEnemies = 0;
    int32 NearbyAllies = 0;
    
    for (AActor* Enemy : DetectedEnemies)
    {
        if (IsValid(Enemy) && GetDistanceToTarget(Enemy) < 800.0f)
            NearbyEnemies++;
    }
    
    for (AActor* Ally : DetectedAllies)
    {
        if (IsValid(Ally) && GetDistanceToTarget(Ally) < 800.0f)
            NearbyAllies++;
    }
    
    return NearbyEnemies > NearbyAllies + 1;
}

bool ASquadMateAIController::ShouldSeekCover() const
{
    return BlackboardComponent && BlackboardComponent->GetValueAsObject(TargetActorKey) != nullptr;
}

bool ASquadMateAIController::ShouldFlank() const
{
    // Flank if we're a scout or if we have tactical advantage
    return AssignedRole == ESquadRole::Scout || 
           (DetectedEnemies.Num() == 1 && DetectedAllies.Num() >= 1);
}

bool ASquadMateAIController::ShouldReviveAlly() const
{
    AActor* AllyToRevive = BlackboardComponent ? 
        Cast<AActor>(BlackboardComponent->GetValueAsObject(AllyToReviveKey)) : nullptr;
    
    if (!AllyToRevive)
        return false;
    
    // Check if it's safe to revive
    return DetectedEnemies.Num() == 0 || GetDistanceToTarget(AllyToRevive) < 300.0f;
}
