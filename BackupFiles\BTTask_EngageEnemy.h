#pragma once

#include "CoreMinimal.h"
#include "BehaviorTree/BTTaskNode.h"
#include "AI/SquadMateAIController.h"
#include "BTTask_EngageEnemy.generated.h"

UENUM(BlueprintType)
enum class EEngagementMode : uint8
{
    Aggressive      UMETA(DisplayName = "Aggressive"),
    Defensive       UMETA(DisplayName = "Defensive"),
    Suppressive     UMETA(DisplayName = "Suppressive"),
    Precise         UMETA(DisplayName = "Precise"),
    Adaptive        UMETA(DisplayName = "Adaptive")
};

UENUM(BlueprintType)
enum class EFireMode : uint8
{
    SingleShot      UMETA(DisplayName = "Single Shot"),
    BurstFire       UMETA(DisplayName = "Burst Fire"),
    FullAuto        UMETA(DisplayName = "Full Auto"),
    Suppressive     UMETA(DisplayName = "Suppressive")
};

USTRUCT(BlueprintType)
struct FEngagementSettings
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float OptimalRange = 500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float MaxEngagementRange = 1200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float MinEngagementRange = 50.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float AccuracyThreshold = 0.7f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float BurstDuration = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float BurstCooldown = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 MinAmmoToEngage = 5;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float HealthThresholdToRetreat = 0.25f;

    FEngagementSettings()
    {
        OptimalRange = 500.0f;
        MaxEngagementRange = 1200.0f;
        MinEngagementRange = 50.0f;
        AccuracyThreshold = 0.7f;
        BurstDuration = 0.5f;
        BurstCooldown = 0.3f;
        MinAmmoToEngage = 5;
        HealthThresholdToRetreat = 0.25f;
    }
};

/**
 * Behavior Tree task for engaging enemies with intelligent combat behavior
 * Handles aiming, firing, movement, and tactical decision making during combat
 */
UCLASS(BlueprintType, meta=(DisplayName="Engage Enemy"))
class SQUADMATEAI_API UBTTask_EngageEnemy : public UBTTaskNode
{
    GENERATED_BODY()

public:
    UBTTask_EngageEnemy();

protected:
    virtual EBTNodeResult::Type ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;
    virtual EBTNodeResult::Type AbortTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;
    virtual void TickTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaSeconds) override;
    virtual void OnTaskFinished(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EBTNodeResult::Type TaskResult) override;
    virtual FString GetStaticDescription() const override;

    // Blackboard Keys
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector TargetActorKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector LastKnownEnemyLocationKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector IsInCombatKey;

    // Engagement Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
    EEngagementMode EngagementMode = EEngagementMode::Adaptive;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
    EFireMode PreferredFireMode = EFireMode::BurstFire;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
    FEngagementSettings EngagementSettings;

    // Aiming Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Aiming")
    float AimingSpeed = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Aiming")
    float AimingAccuracy = 0.8f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Aiming")
    float PredictionFactor = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Aiming")
    bool bUseAdvancedPrediction = true;

    // Movement During Combat
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    bool bAllowMovementDuringCombat = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    float StrafingSpeed = 300.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    float StrafingDistance = 200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    bool bUseCoverDuringEngagement = true;

    // Stance Management
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stance")
    bool bAutoAdjustStance = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stance")
    float CrouchEngagementRange = 300.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stance")
    float ProneEngagementRange = 600.0f;

    // Performance Settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MaxEngagementDuration = 30.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float TargetUpdateFrequency = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseLineOfSightChecks = true;

    // Debug Options
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bDrawDebugInfo = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bLogEngagementEvents = false;

public:
    // Static utility functions
    UFUNCTION(BlueprintCallable, Category = "Combat", CallInEditor = true)
    static bool CanEngageTarget(AActor* Agent, AActor* Target, float MaxRange = 1200.0f);

    UFUNCTION(BlueprintCallable, Category = "Combat", CallInEditor = true)
    static float CalculateEngagementScore(AActor* Agent, AActor* Target, EEngagementMode Mode);

    UFUNCTION(BlueprintCallable, Category = "Combat", CallInEditor = true)
    static FVector PredictTargetLocation(AActor* Target, float PredictionTime = 0.5f);

protected:
    // Core engagement logic
    bool InitializeEngagement(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    void UpdateEngagement(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    void TerminateEngagement(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);

    // Target management
    bool ValidateTarget(AActor* Target, AActor* Agent);
    bool UpdateTargetTracking(UBehaviorTreeComponent& OwnerComp, AActor* Target);
    FVector GetTargetAimPoint(AActor* Target, AActor* Agent);
    bool HasLineOfSightToTarget(AActor* Agent, AActor* Target);

    // Aiming system
    void UpdateAiming(UBehaviorTreeComponent& OwnerComp, const FVector& TargetLocation, float DeltaTime);
    FRotator CalculateAimRotation(const FVector& AgentLocation, const FVector& TargetLocation);
    FVector ApplyAimingError(const FVector& PerfectAim, float AccuracyFactor);
    bool IsAimedAtTarget(const FRotator& CurrentAim, const FVector& TargetLocation, const FVector& AgentLocation);

    // Firing control
    void UpdateFiring(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    bool ShouldStartFiring(UBehaviorTreeComponent& OwnerComp, AActor* Target);
    bool ShouldStopFiring(UBehaviorTreeComponent& OwnerComp, AActor* Target);
    void ProcessFireMode(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);

    // Movement and positioning
    void UpdateCombatMovement(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    void PerformStrafing(UBehaviorTreeComponent& OwnerComp, const FVector& EnemyLocation);
    void AdjustStanceForRange(UBehaviorTreeComponent& OwnerComp, float DistanceToTarget);

    // Tactical decisions
    bool ShouldRetreat(UBehaviorTreeComponent& OwnerComp, AActor* Target);
    bool ShouldSeekCover(UBehaviorTreeComponent& OwnerComp, AActor* Target);
    bool ShouldReload(UBehaviorTreeComponent& OwnerComp);
    EFireMode DetermineOptimalFireMode(float DistanceToTarget, float TargetSpeed);

    // Utility functions
    float GetDistanceToTarget(AActor* Agent, AActor* Target);
    float GetTargetSpeed(AActor* Target);
    bool IsTargetMoving(AActor* Target, float MinSpeed = 50.0f);
    FVector GetAgentWeaponLocation(AActor* Agent);

    // Debug and logging
    void LogEngagementEvent(const FString& Event, UBehaviorTreeComponent& OwnerComp);
    void DrawDebugEngagementInfo(UWorld* World, const FVector& AgentLocation, 
                               const FVector& TargetLocation, bool bHasLineOfSight);

private:
    // Task memory structure
    struct FBTTask_EngageEnemyMemory
    {
        TWeakObjectPtr<AActor> CurrentTarget;
        float EngagementStartTime = 0.0f;
        float LastShotTime = 0.0f;
        float LastTargetUpdateTime = 0.0f;
        float BurstStartTime = 0.0f;
        bool bIsFiring = false;
        bool bIsAiming = false;
        bool bInBurstMode = false;
        EFireMode CurrentFireMode = EFireMode::SingleShot;
        FVector LastKnownTargetLocation = FVector::ZeroVector;
        FVector LastTargetVelocity = FVector::ZeroVector;
        int32 ShotsFiredInBurst = 0;
        float AccumulatedAimError = 0.0f;
    };

    // Memory management
    void InitializeTaskMemory(uint8* NodeMemory);
    void CleanupTaskMemory(uint8* NodeMemory);
    FBTTask_EngageEnemyMemory* GetTaskMemory(uint8* NodeMemory);

    // Engagement mode specific behavior
    void ProcessAggressiveMode(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    void ProcessDefensiveMode(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    void ProcessSuppressiveMode(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    void ProcessPreciseMode(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    void ProcessAdaptiveMode(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);

    // Fire mode implementations
    void ProcessSingleShotMode(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    void ProcessBurstFireMode(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    void ProcessFullAutoMode(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    void ProcessSuppressiveMode(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
};
