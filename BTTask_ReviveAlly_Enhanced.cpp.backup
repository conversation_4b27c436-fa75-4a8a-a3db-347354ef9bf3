#include "BehaviorTree/Tasks/BTTask_ReviveAlly_Enhanced.h"
#include "AIController.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "GameFramework/Character.h"
#include "GameFramework/Pawn.h"
#include "Engine/World.h"
#include "DrawDebugHelpers.h"
#include "AI/SquadMateAIController.h"
#include "Components/HealthComponent.h"
#include "Components/ReviveComponent.h"
#include "SquadMateAI.h"
#include "Kismet/GameplayStatics.h"

UBTTask_ReviveAlly_Enhanced::UBTTask_ReviveAlly_Enhanced()
{
    NodeName = TEXT("Revive Ally Enhanced");
    bNotifyTick = true;
    bNotifyTaskFinished = true;
    
    // Set default blackboard keys
    ReviveTargetKey.SelectedKeyName = FName("ReviveTarget");
    IsRevivingKey.SelectedKeyName = FName("IsReviving");
    IsUnderFireKey.SelectedKeyName = FName("IsUnderFire");
    SquadRoleKey.SelectedKeyName = FName("SquadRole");
    CoverLocationKey.SelectedKeyName = FName("CoverLocation");

    // Initialize role modifiers
    RoleReviveTimeModifiers.Add(TEXT("Support"), 0.8f);
    RoleReviveTimeModifiers.Add(TEXT("Assault"), 1.0f);
    RoleReviveTimeModifiers.Add(TEXT("Scout"), 1.1f);
    RoleReviveTimeModifiers.Add(TEXT("Sniper"), 1.2f);
    RoleReviveTimeModifiers.Add(TEXT("Anchor"), 0.9f);

    RoleSafetyModifiers.Add(TEXT("Support"), 1.2f);
    RoleSafetyModifiers.Add(TEXT("Assault"), 0.8f);
    RoleSafetyModifiers.Add(TEXT("Scout"), 1.0f);
    RoleSafetyModifiers.Add(TEXT("Sniper"), 1.5f);
    RoleSafetyModifiers.Add(TEXT("Anchor"), 1.1f);

    // Initialize callouts
    ReviveStartCallouts = {
        TEXT("Moving to revive teammate!"),
        TEXT("Going for the revive!"),
        TEXT("I've got you, hold on!"),
        TEXT("Reviving ally, cover me!")
    };

    ReviveProgressCallouts = {
        TEXT("Revive in progress..."),
        TEXT("Almost got them up!"),
        TEXT("Keep covering, nearly done!"),
        TEXT("Hang in there!")
    };

    ReviveCompleteCallouts = {
        TEXT("Teammate revived!"),
        TEXT("They're back up!"),
        TEXT("Revive successful!"),
        TEXT("Got them back in the fight!")
    };

    ReviveFailedCallouts = {
        TEXT("Revive failed!"),
        TEXT("Couldn't get to them!"),
        TEXT("Too dangerous to revive!"),
        TEXT("Need better cover!")
    };
}

EBTNodeResult::Type UBTTask_ReviveAlly_Enhanced::ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    // Initialize enhanced task memory
    InitializeEnhancedTaskMemory(NodeMemory);
    
    AAIController* AIController = OwnerComp.GetAIOwner();
    if (!AIController || !AIController->GetPawn())
    {
        UE_LOG(LogSquadMateAI, Warning, TEXT("BTTask_ReviveAlly_Enhanced: Invalid AI Controller or Pawn"));
        return EBTNodeResult::Failed;
    }

    UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
    if (!BlackboardComp)
    {
        UE_LOG(LogSquadMateAI, Warning, TEXT("BTTask_ReviveAlly_Enhanced: No Blackboard Component"));
        return EBTNodeResult::Failed;
    }

    // Get revive target from blackboard
    AActor* ReviveTarget = Cast<AActor>(BlackboardComp->GetValueAsObject(ReviveTargetKey.SelectedKeyName));
    if (!ReviveTarget)
    {
        UE_LOG(LogSquadMateAI, Warning, TEXT("BTTask_ReviveAlly_Enhanced: No valid revive target"));
        return EBTNodeResult::Failed;
    }

    // Enhanced validation
    if (!ValidateReviveConditions(ReviveTarget, AIController->GetPawn()))
    {
        UE_LOG(LogSquadMateAI, Warning, TEXT("BTTask_ReviveAlly_Enhanced: Enhanced validation failed"));
        return EBTNodeResult::Failed;
    }

    // Initialize enhanced revive process
    if (!InitializeEnhancedRevive(OwnerComp, NodeMemory))
    {
        UE_LOG(LogSquadMateAI, Warning, TEXT("BTTask_ReviveAlly_Enhanced: Failed to initialize enhanced revive"));
        return EBTNodeResult::Failed;
    }

    FBTTask_ReviveAllyEnhancedMemory* TaskMemory = GetEnhancedTaskMemory(NodeMemory);
    if (!TaskMemory)
    {
        return EBTNodeResult::Failed;
    }

    // Determine optimal approach strategy
    if (bAdaptToThreatLevel)
    {
        TaskMemory->ThreatLevel = AssessThreatLevel(ReviveTarget->GetActorLocation(), ThreatAssessmentRadius);
        TaskMemory->UsedApproach = DetermineOptimalApproach(AIController->GetPawn(), ReviveTarget, TaskMemory->ThreatLevel);
    }
    else
    {
        TaskMemory->UsedApproach = ApproachStrategy;
    }

    // Calculate role-modified revive time
    TaskMemory->CalculatedReviveTime = CalculateRoleModifiedReviveTime(AIController->GetPawn());

    // Set enhanced reviving state
    SetEnhancedRevivingState(OwnerComp, true, TaskMemory->UsedApproach);

    // Team coordination
    if (bUseTeamCoordination)
    {
        BroadcastReviveIntent(OwnerComp, ReviveTarget);
        
        if (bRequestCoverFire)
        {
            RequestCoverFire(OwnerComp, ReviveTarget);
            TaskMemory->bCoverFireRequested = true;
        }

        if (bCoordinateWithNearbyAllies)
        {
            CoordinateWithNearbyAllies(OwnerComp, ReviveTarget);
            TaskMemory->bTeamCoordinated = true;
        }
    }

    // Execute approach strategy
    bool bApproachSuccess = false;
    switch (TaskMemory->UsedApproach)
    {
        case EReviveApproach::Direct:
            bApproachSuccess = ExecuteDirectApproach(OwnerComp, NodeMemory);
            break;
        case EReviveApproach::Cautious:
            bApproachSuccess = ExecuteCautiousApproach(OwnerComp, NodeMemory);
            break;
        case EReviveApproach::Stealth:
            bApproachSuccess = ExecuteStealthApproach(OwnerComp, NodeMemory);
            break;
        case EReviveApproach::CoverToCover:
            bApproachSuccess = ExecuteCoverToCoverApproach(OwnerComp, NodeMemory);
            break;
        case EReviveApproach::TeamCoordinated:
            bApproachSuccess = ExecuteTeamCoordinatedApproach(OwnerComp, NodeMemory);
            break;
    }

    if (!bApproachSuccess)
    {
        CompleteEnhancedRevive(OwnerComp, NodeMemory, EReviveResult::Failed);
        return EBTNodeResult::Failed;
    }

    // Start revive process
    TaskMemory->bReviveInProgress = true;
    TaskMemory->ReviveStartTime = GetWorld()->GetTimeSeconds();

    // Play contextual callout
    PlayContextualCallout(OwnerComp, ReviveStartCallouts);

    // Log revive start
    if (bLogReviveEvents)
    {
        LogEnhancedReviveEvent(TEXT("Enhanced Revive Started"), OwnerComp, ReviveTarget);
    }

    return EBTNodeResult::InProgress;
}

EBTNodeResult::Type UBTTask_ReviveAlly_Enhanced::AbortTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    CompleteEnhancedRevive(OwnerComp, NodeMemory, EReviveResult::Interrupted);
    return EBTNodeResult::Aborted;
}

void UBTTask_ReviveAlly_Enhanced::TickTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaSeconds)
{
    FBTTask_ReviveAllyEnhancedMemory* TaskMemory = GetEnhancedTaskMemory(NodeMemory);
    if (!TaskMemory)
    {
        FinishLatentTask(OwnerComp, EBTNodeResult::Failed);
        return;
    }

    // Update enhanced revive progress
    UpdateEnhancedRevive(OwnerComp, NodeMemory, DeltaSeconds);

    // Enhanced safety checks
    float CurrentTime = GetWorld()->GetTimeSeconds();
    if (CurrentTime - TaskMemory->LastSafetyCheck >= ReviveConfig.SafetyRadius / 1000.0f) // Dynamic safety check frequency
    {
        if (!AssessEnvironmentalSafety(TaskMemory->RevivePosition, OwnerComp.GetAIOwner()->GetPawn()))
        {
            CompleteEnhancedRevive(OwnerComp, NodeMemory, EReviveResult::EnvironmentUnsafe);
            FinishLatentTask(OwnerComp, EBTNodeResult::Failed);
            return;
        }
        TaskMemory->LastSafetyCheck = CurrentTime;
    }

    // Progress updates to team
    if (ReviveConfig.bBroadcastProgress && CurrentTime - TaskMemory->LastProgressUpdate >= 1.0f)
    {
        UpdateTeamOnProgress(OwnerComp, TaskMemory->ReviveProgress);
        TaskMemory->LastProgressUpdate = CurrentTime;
    }

    // Check if revive is complete
    if (TaskMemory->ReviveProgress >= 1.0f)
    {
        CompleteEnhancedRevive(OwnerComp, NodeMemory, EReviveResult::Success);
        FinishLatentTask(OwnerComp, EBTNodeResult::Succeeded);
        return;
    }

    // Draw debug info
    if (bDrawDebugInfo)
    {
        DrawEnhancedDebugInfo(GetWorld(), TaskMemory);
    }
}

void UBTTask_ReviveAlly_Enhanced::OnTaskFinished(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EBTNodeResult::Type TaskResult)
{
    // Cleanup enhanced task memory
    CleanupEnhancedTaskMemory(NodeMemory);
    
    // Reset blackboard state
    SetEnhancedRevivingState(OwnerComp, false, EReviveApproach::Direct);
    
    Super::OnTaskFinished(OwnerComp, NodeMemory, TaskResult);
}

FString UBTTask_ReviveAlly_Enhanced::GetStaticDescription() const
{
    return FString::Printf(TEXT("Enhanced Revive Ally: %s approach, %.1fs base time, %s coordination"),
        *UEnum::GetValueAsString(ApproachStrategy),
        ReviveConfig.BaseReviveTime,
        bUseTeamCoordination ? TEXT("with") : TEXT("without"));
}

// Static utility functions
bool UBTTask_ReviveAlly_Enhanced::CanReviveTargetEnhanced(AActor* Reviver, AActor* Target, const FReviveConfiguration& Config)
{
    if (!Reviver || !Target)
    {
        return false;
    }

    // Check distance
    float Distance = FVector::Dist(Reviver->GetActorLocation(), Target->GetActorLocation());
    if (Distance > Config.MaxReviveDistance)
    {
        return false;
    }

    // Check reviver health
    if (UHealthComponent* HealthComp = Reviver->FindComponentByClass<UHealthComponent>())
    {
        if (HealthComp->GetHealthPercentage() < Config.MinHealthToRevive / 100.0f)
        {
            return false;
        }
    }

    // Check if target can be revived
    if (UHealthComponent* TargetHealthComp = Target->FindComponentByClass<UHealthComponent>())
    {
        if (!TargetHealthComp->CanBeRevived())
        {
            return false;
        }
    }

    return true;
}

EReviveApproach UBTTask_ReviveAlly_Enhanced::DetermineOptimalApproach(AActor* Reviver, AActor* Target, float ThreatLevel)
{
    if (ThreatLevel > 0.8f)
    {
        return EReviveApproach::TeamCoordinated;
    }
    else if (ThreatLevel > 0.6f)
    {
        return EReviveApproach::CoverToCover;
    }
    else if (ThreatLevel > 0.4f)
    {
        return EReviveApproach::Cautious;
    }
    else if (ThreatLevel > 0.2f)
    {
        return EReviveApproach::Stealth;
    }
    else
    {
        return EReviveApproach::Direct;
    }
}

float UBTTask_ReviveAlly_Enhanced::CalculateReviveTime(AActor* Reviver, const FReviveConfiguration& Config)
{
    float BaseTime = Config.BaseReviveTime;
    float RoleModifier = Config.RoleModifier;
    
    // Additional modifiers can be added here based on equipment, perks, etc.
    
    return BaseTime * RoleModifier;
}

float UBTTask_ReviveAlly_Enhanced::AssessThreatLevel(const FVector& Location, float Radius)
{
    // This would implement actual threat assessment logic
    // For now, return a placeholder value
    // In a real implementation, this would:
    // - Count nearby enemies
    // - Check for sniper positions
    // - Assess cover availability
    // - Consider recent combat activity

    return 0.3f; // Placeholder: moderate threat
}

// Enhanced Core Logic Implementation
bool UBTTask_ReviveAlly_Enhanced::InitializeEnhancedRevive(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    FBTTask_ReviveAllyEnhancedMemory* TaskMemory = GetEnhancedTaskMemory(NodeMemory);
    if (!TaskMemory)
    {
        return false;
    }

    UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent();
    AActor* ReviveTarget = Cast<AActor>(BlackboardComp->GetValueAsObject(ReviveTargetKey.SelectedKeyName));

    if (!ReviveTarget)
    {
        return false;
    }

    TaskMemory->ReviveTarget = ReviveTarget;
    TaskMemory->RevivePosition = ReviveTarget->GetActorLocation();
    TaskMemory->ReviveStartTime = GetWorld()->GetTimeSeconds();
    TaskMemory->ReviveProgress = 0.0f;
    TaskMemory->bReviveInProgress = false;

    return true;
}

void UBTTask_ReviveAlly_Enhanced::UpdateEnhancedRevive(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime)
{
    FBTTask_ReviveAllyEnhancedMemory* TaskMemory = GetEnhancedTaskMemory(NodeMemory);
    if (!TaskMemory || !TaskMemory->bReviveInProgress)
    {
        return;
    }

    // Update progress based on calculated revive time
    float ElapsedTime = GetWorld()->GetTimeSeconds() - TaskMemory->ReviveStartTime;
    TaskMemory->ReviveProgress = FMath::Clamp(ElapsedTime / TaskMemory->CalculatedReviveTime, 0.0f, 1.0f);

    // Update blackboard state
    UpdateBlackboardEnhancedState(OwnerComp, TaskMemory);
}

void UBTTask_ReviveAlly_Enhanced::CompleteEnhancedRevive(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EReviveResult Result)
{
    FBTTask_ReviveAllyEnhancedMemory* TaskMemory = GetEnhancedTaskMemory(NodeMemory);
    if (!TaskMemory)
    {
        return;
    }

    TaskMemory->bReviveInProgress = false;
    TaskMemory->LastResult = Result;

    // Handle result-specific actions
    switch (Result)
    {
        case EReviveResult::Success:
            if (TaskMemory->ReviveTarget.IsValid())
            {
                // Execute actual revive
                if (UReviveComponent* ReviveComp = GetReviveComponent(TaskMemory->ReviveTarget.Get()))
                {
                    ReviveComp->CompleteRevive();
                }
                PlayContextualCallout(OwnerComp, ReviveCompleteCallouts);
            }
            break;

        case EReviveResult::Failed:
        case EReviveResult::Interrupted:
        case EReviveResult::EnvironmentUnsafe:
            PlayContextualCallout(OwnerComp, ReviveFailedCallouts);
            break;
    }

    // Log completion
    if (bLogReviveEvents)
    {
        LogEnhancedReviveEvent(FString::Printf(TEXT("Enhanced Revive Completed: %s"),
            *UEnum::GetValueAsString(Result)), OwnerComp, TaskMemory->ReviveTarget.Get());
    }
}

// Approach Strategy Implementations
bool UBTTask_ReviveAlly_Enhanced::ExecuteDirectApproach(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    // Simple direct movement to target
    FBTTask_ReviveAllyEnhancedMemory* TaskMemory = GetEnhancedTaskMemory(NodeMemory);
    if (!TaskMemory || !TaskMemory->ReviveTarget.IsValid())
    {
        return false;
    }

    TaskMemory->SafeApproachPosition = TaskMemory->ReviveTarget->GetActorLocation();
    return true;
}

bool UBTTask_ReviveAlly_Enhanced::ExecuteCautiousApproach(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    // Slower, more careful approach with frequent safety checks
    FBTTask_ReviveAllyEnhancedMemory* TaskMemory = GetEnhancedTaskMemory(NodeMemory);
    if (!TaskMemory || !TaskMemory->ReviveTarget.IsValid())
    {
        return false;
    }

    // Find a safer approach position
    FVector TargetLocation = TaskMemory->ReviveTarget->GetActorLocation();
    FVector SaferPosition = TargetLocation; // In real implementation, find cover-adjacent position

    TaskMemory->SafeApproachPosition = SaferPosition;
    TaskMemory->CalculatedReviveTime *= 1.2f; // 20% longer due to cautious approach

    return true;
}

bool UBTTask_ReviveAlly_Enhanced::ExecuteStealthApproach(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    // Stealthy approach avoiding enemy detection
    FBTTask_ReviveAllyEnhancedMemory* TaskMemory = GetEnhancedTaskMemory(NodeMemory);
    if (!TaskMemory || !TaskMemory->ReviveTarget.IsValid())
    {
        return false;
    }

    // Implement stealth pathing logic
    TaskMemory->SafeApproachPosition = TaskMemory->ReviveTarget->GetActorLocation();
    TaskMemory->CalculatedReviveTime *= 1.5f; // 50% longer due to stealth requirements

    return true;
}

bool UBTTask_ReviveAlly_Enhanced::ExecuteCoverToCoverApproach(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    // Move from cover to cover until reaching target
    FBTTask_ReviveAllyEnhancedMemory* TaskMemory = GetEnhancedTaskMemory(NodeMemory);
    if (!TaskMemory || !TaskMemory->ReviveTarget.IsValid())
    {
        return false;
    }

    // Find cover positions between current location and target
    TaskMemory->SafeApproachPosition = TaskMemory->ReviveTarget->GetActorLocation();
    TaskMemory->CalculatedReviveTime *= 1.3f; // 30% longer due to cover movement

    return true;
}

bool UBTTask_ReviveAlly_Enhanced::ExecuteTeamCoordinatedApproach(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory)
{
    // Coordinate with team for maximum safety
    FBTTask_ReviveAllyEnhancedMemory* TaskMemory = GetEnhancedTaskMemory(NodeMemory);
    if (!TaskMemory || !TaskMemory->ReviveTarget.IsValid())
    {
        return false;
    }

    // Wait for team coordination before proceeding
    TaskMemory->SafeApproachPosition = TaskMemory->ReviveTarget->GetActorLocation();
    TaskMemory->CalculatedReviveTime *= 0.9f; // 10% faster due to team support

    return true;
}

// Team Coordination Functions
void UBTTask_ReviveAlly_Enhanced::RequestCoverFire(UBehaviorTreeComponent& OwnerComp, AActor* Target)
{
    if (ASquadMateAIController* AIController = GetSquadMateController(OwnerComp))
    {
        FString CoverRequest = FString::Printf(TEXT("Need cover fire for revive at %s!"),
            *Target->GetActorLocation().ToString());
        BroadcastToSquadEnhanced(OwnerComp, CoverRequest, 0.9f);
    }
}

void UBTTask_ReviveAlly_Enhanced::CoordinateWithNearbyAllies(UBehaviorTreeComponent& OwnerComp, AActor* Target)
{
    // Find nearby allies and coordinate revive attempt
    TArray<AActor*> NearbyAllies;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), ASquadMateAIController::StaticClass(), NearbyAllies);

    for (AActor* Ally : NearbyAllies)
    {
        float Distance = FVector::Dist(OwnerComp.GetAIOwner()->GetPawn()->GetActorLocation(), Ally->GetActorLocation());
        if (Distance <= TeamCoordinationRadius)
        {
            // Send coordination message to nearby ally
            if (bLogTeamCoordination)
            {
                UE_LOG(LogSquadMateAI, Log, TEXT("Coordinating revive with ally at distance: %.1f"), Distance);
            }
        }
    }
}

void UBTTask_ReviveAlly_Enhanced::BroadcastReviveIntent(UBehaviorTreeComponent& OwnerComp, AActor* Target)
{
    FString IntentMessage = FString::Printf(TEXT("Attempting revive on %s"),
        Target ? *Target->GetName() : TEXT("Unknown"));
    BroadcastToSquadEnhanced(OwnerComp, IntentMessage, 0.7f);
}

void UBTTask_ReviveAlly_Enhanced::UpdateTeamOnProgress(UBehaviorTreeComponent& OwnerComp, float Progress)
{
    if (Progress >= 0.5f && Progress < 0.8f)
    {
        PlayContextualCallout(OwnerComp, ReviveProgressCallouts);
    }
}

// Enhanced Communication Functions
void UBTTask_ReviveAlly_Enhanced::PlayContextualCallout(UBehaviorTreeComponent& OwnerComp, const TArray<FString>& Callouts)
{
    if (Callouts.Num() > 0)
    {
        FString SelectedCallout = GetRandomCallout(Callouts);
        BroadcastToSquadEnhanced(OwnerComp, SelectedCallout, 0.8f);
    }
}

FString UBTTask_ReviveAlly_Enhanced::GetRandomCallout(const TArray<FString>& Callouts)
{
    if (Callouts.Num() == 0)
    {
        return TEXT("Default callout");
    }

    int32 RandomIndex = FMath::RandRange(0, Callouts.Num() - 1);
    return Callouts[RandomIndex];
}

void UBTTask_ReviveAlly_Enhanced::BroadcastToSquadEnhanced(UBehaviorTreeComponent& OwnerComp, const FString& Message, float Priority)
{
    if (ASquadMateAIController* AIController = GetSquadMateController(OwnerComp))
    {
        AIController->BroadcastToSquad(Message, Priority);
    }
}

// Enhanced Validation Functions
bool UBTTask_ReviveAlly_Enhanced::ValidateReviveConditions(AActor* Target, AActor* Reviver)
{
    if (!Target || !Reviver)
    {
        return false;
    }

    // Enhanced validation using configuration
    if (!CanReviveTargetEnhanced(Reviver, Target, ReviveConfig))
    {
        return false;
    }

    // Check if teammate is busy with another task
    if (!CheckTeammateAvailability(Reviver))
    {
        return false;
    }

    return true;
}

bool UBTTask_ReviveAlly_Enhanced::AssessEnvironmentalSafety(const FVector& Position, AActor* Reviver)
{
    // Check for nearby threats
    TArray<AActor*> NearbyThreats = GetNearbyThreats(Position, ReviveConfig.SafetyRadius);
    if (NearbyThreats.Num() > 0)
    {
        return false;
    }

    // Check for sniper threats if enabled
    if (bCheckForSnipers && CheckForSniperThreats(Position, ThreatAssessmentRadius))
    {
        return false;
    }

    // Check if in open area
    if (bAvoidOpenAreas && IsInOpenArea(Position))
    {
        return false;
    }

    return true;
}

bool UBTTask_ReviveAlly_Enhanced::CheckTeammateAvailability(AActor* Reviver)
{
    // Check if reviver is already engaged in critical tasks
    if (ASquadMateAIController* AIController = Cast<ASquadMateAIController>(Cast<APawn>(Reviver)->GetController()))
    {
        // Check blackboard for current state
        if (UBlackboardComponent* BB = AIController->GetBlackboardComponent())
        {
            bool bIsUnderFire = BB->GetValueAsBool(IsUnderFireKey.SelectedKeyName);
            if (bIsUnderFire)
            {
                return false;
            }
        }
    }

    return true;
}

float UBTTask_ReviveAlly_Enhanced::CalculateRoleModifiedReviveTime(AActor* Reviver)
{
    float BaseTime = ReviveConfig.BaseReviveTime;

    // Get role from blackboard or component
    if (ASquadMateAIController* AIController = Cast<ASquadMateAIController>(Cast<APawn>(Reviver)->GetController()))
    {
        if (UBlackboardComponent* BB = AIController->GetBlackboardComponent())
        {
            FString Role = BB->GetValueAsString(SquadRoleKey.SelectedKeyName);
            if (float* Modifier = RoleReviveTimeModifiers.Find(Role))
            {
                BaseTime *= *Modifier;
            }
        }
    }

    return BaseTime;
}

// Advanced Safety Functions
bool UBTTask_ReviveAlly_Enhanced::CheckForSniperThreats(const FVector& Position, float Radius)
{
    // Implement sniper detection logic
    // This would check for long-range threats and elevated positions
    return false; // Placeholder
}

bool UBTTask_ReviveAlly_Enhanced::IsInOpenArea(const FVector& Position)
{
    // Check if position has adequate cover nearby
    // This would use EQS or custom cover detection
    return false; // Placeholder
}

float UBTTask_ReviveAlly_Enhanced::CalculatePositionExposure(const FVector& Position)
{
    // Calculate how exposed a position is to enemy fire
    // Returns 0.0 (fully covered) to 1.0 (completely exposed)
    return 0.3f; // Placeholder
}

TArray<AActor*> UBTTask_ReviveAlly_Enhanced::GetNearbyThreats(const FVector& Location, float Radius)
{
    TArray<AActor*> Threats;
    // Implement threat detection logic
    // This would find enemy actors within radius
    return Threats;
}

// Memory Management Functions
FBTTask_ReviveAlly_Enhanced::FBTTask_ReviveAllyEnhancedMemory* UBTTask_ReviveAlly_Enhanced::GetEnhancedTaskMemory(uint8* NodeMemory)
{
    return reinterpret_cast<FBTTask_ReviveAllyEnhancedMemory*>(NodeMemory);
}

void UBTTask_ReviveAlly_Enhanced::InitializeEnhancedTaskMemory(uint8* NodeMemory)
{
    FBTTask_ReviveAllyEnhancedMemory* TaskMemory = GetEnhancedTaskMemory(NodeMemory);
    if (TaskMemory)
    {
        // Initialize all memory values
        TaskMemory->ReviveTarget = nullptr;
        TaskMemory->ReviveStartTime = 0.0f;
        TaskMemory->LastSafetyCheck = 0.0f;
        TaskMemory->LastProgressUpdate = 0.0f;
        TaskMemory->ReviveProgress = 0.0f;
        TaskMemory->CalculatedReviveTime = ReviveConfig.BaseReviveTime;
        TaskMemory->ThreatLevel = 0.0f;
        TaskMemory->bReviveInProgress = false;
        TaskMemory->bAnimationPlaying = false;
        TaskMemory->bEffectsPlaying = false;
        TaskMemory->bTeamCoordinated = false;
        TaskMemory->bCoverFireRequested = false;
        TaskMemory->RevivePosition = FVector::ZeroVector;
        TaskMemory->SafeApproachPosition = FVector::ZeroVector;
        TaskMemory->LastResult = EReviveResult::Success;
        TaskMemory->UsedApproach = EReviveApproach::Direct;
        TaskMemory->CoordinatedAllies.Empty();
    }
}

void UBTTask_ReviveAlly_Enhanced::CleanupEnhancedTaskMemory(uint8* NodeMemory)
{
    FBTTask_ReviveAllyEnhancedMemory* TaskMemory = GetEnhancedTaskMemory(NodeMemory);
    if (TaskMemory)
    {
        // Clean up any references
        TaskMemory->ReviveTarget = nullptr;
        TaskMemory->CoordinatedAllies.Empty();
    }
}

// Component Access Helpers
UHealthComponent* UBTTask_ReviveAlly_Enhanced::GetHealthComponent(AActor* Actor)
{
    return Actor ? Actor->FindComponentByClass<UHealthComponent>() : nullptr;
}

UReviveComponent* UBTTask_ReviveAlly_Enhanced::GetReviveComponent(AActor* Actor)
{
    return Actor ? Actor->FindComponentByClass<UReviveComponent>() : nullptr;
}

ASquadMateAIController* UBTTask_ReviveAlly_Enhanced::GetSquadMateController(UBehaviorTreeComponent& OwnerComp)
{
    return Cast<ASquadMateAIController>(OwnerComp.GetAIOwner());
}

// Enhanced State Management
void UBTTask_ReviveAlly_Enhanced::SetEnhancedRevivingState(UBehaviorTreeComponent& OwnerComp, bool bIsReviving, EReviveApproach Approach)
{
    if (UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent())
    {
        BlackboardComp->SetValueAsBool(IsRevivingKey.SelectedKeyName, bIsReviving);

        // Store approach strategy in blackboard for other systems to use
        BlackboardComp->SetValueAsString(TEXT("ReviveApproach"), UEnum::GetValueAsString(Approach));
    }
}

void UBTTask_ReviveAlly_Enhanced::UpdateBlackboardEnhancedState(UBehaviorTreeComponent& OwnerComp, const FBTTask_ReviveAllyEnhancedMemory* TaskMemory)
{
    if (!TaskMemory)
    {
        return;
    }

    if (UBlackboardComponent* BlackboardComp = OwnerComp.GetBlackboardComponent())
    {
        // Update revive progress
        BlackboardComp->SetValueAsFloat(TEXT("ReviveProgress"), TaskMemory->ReviveProgress);

        // Update threat level
        BlackboardComp->SetValueAsFloat(TEXT("ThreatLevel"), TaskMemory->ThreatLevel);

        // Update coordination status
        BlackboardComp->SetValueAsBool(TEXT("TeamCoordinated"), TaskMemory->bTeamCoordinated);
    }
}

// Debug and Logging Functions
void UBTTask_ReviveAlly_Enhanced::LogEnhancedReviveEvent(const FString& Event, UBehaviorTreeComponent& OwnerComp, AActor* Target)
{
    if (bLogReviveEvents)
    {
        FString TargetName = Target ? Target->GetName() : TEXT("None");
        FString ControllerName = OwnerComp.GetAIOwner() ? OwnerComp.GetAIOwner()->GetName() : TEXT("None");

        UE_LOG(LogSquadMateAI, Log, TEXT("Enhanced Revive Event - Controller: %s, Target: %s, Event: %s"),
            *ControllerName, *TargetName, *Event);
    }
}

void UBTTask_ReviveAlly_Enhanced::DrawEnhancedDebugInfo(UWorld* World, const FBTTask_ReviveAllyEnhancedMemory* TaskMemory)
{
    if (!World || !TaskMemory || !bDrawDebugInfo)
    {
        return;
    }

    // Draw revive progress circle
    if (TaskMemory->ReviveTarget.IsValid())
    {
        FVector TargetLocation = TaskMemory->ReviveTarget->GetActorLocation();

        // Progress circle
        float Radius = 100.0f;
        FColor ProgressColor = FColor::Lerp(FColor::Red, FColor::Green, TaskMemory->ReviveProgress);
        DrawDebugCircle(World, TargetLocation, Radius, 32, ProgressColor, false, 0.1f, 0, 2.0f, FVector(0, 1, 0), FVector(1, 0, 0));

        // Safety radius
        DrawDebugCircle(World, TargetLocation, ReviveConfig.SafetyRadius, 64, FColor::Yellow, false, 0.1f, 0, 1.0f);

        // Approach path
        if (TaskMemory->SafeApproachPosition != FVector::ZeroVector)
        {
            DrawDebugLine(World, TaskMemory->SafeApproachPosition, TargetLocation, FColor::Blue, false, 0.1f, 0, 3.0f);
        }

        // Threat level indicator
        FString ThreatText = FString::Printf(TEXT("Threat: %.2f"), TaskMemory->ThreatLevel);
        DrawDebugString(World, TargetLocation + FVector(0, 0, 150), ThreatText, nullptr, FColor::White, 0.1f);

        // Approach strategy
        FString ApproachText = UEnum::GetValueAsString(TaskMemory->UsedApproach);
        DrawDebugString(World, TargetLocation + FVector(0, 0, 120), ApproachText, nullptr, FColor::Cyan, 0.1f);
    }
}
