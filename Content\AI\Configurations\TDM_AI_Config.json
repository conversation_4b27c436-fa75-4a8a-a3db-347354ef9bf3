{"tdm_ai_configuration": {"version": "1.0", "description": "PUBG Mobile 5v5 TDM AI Configuration", "decision_tree": {"nodes": [{"name": "immediate_threat_response", "priority": 1, "cooldown": 0.1, "conditions": ["enemy_visible", "being_shot_at"], "actions": ["return_fire", "seek_cover", "call_enemy_position"]}, {"name": "revive_priority", "priority": 2, "cooldown": 1.0, "conditions": ["ally_down", "safe_to_revive", "not_under_fire"], "actions": ["move_to_ally", "start_revive", "watch_for_enemies"]}, {"name": "aggressive_engagement", "priority": 3, "cooldown": 0.2, "conditions": ["enemy_spotted", "health_above_50", "ammo_sufficient", "has_line_of_sight"], "actions": ["engage_target", "coordinate_with_team", "control_recoil", "strafe_movement"]}, {"name": "flanking_maneuver", "priority": 4, "cooldown": 2.0, "conditions": ["enemy_spotted", "no_line_of_sight", "flanker_role", "team_engaging"], "actions": ["find_flank_route", "move_to_flank", "coordinate_timing", "engage_from_side"]}, {"name": "suppressive_fire", "priority": 5, "cooldown": 1.5, "conditions": ["support_role", "teammates_advancing", "enemy_in_cover"], "actions": ["suppress_enemy_position", "provide_cover_fire", "communicate_enemy_movement"]}, {"name": "defensive_retreat", "priority": 6, "cooldown": 0.5, "conditions": ["health_below_30", "outnumbered", "low_ammo"], "actions": ["find_cover", "retreat_to_spawn", "call_for_support", "defensive_position"]}, {"name": "tactical_positioning", "priority": 7, "cooldown": 1.0, "conditions": ["no_immediate_threat", "zone_control_needed", "team_coordination_required"], "actions": ["move_to_strategic_position", "watch_angles", "support_team", "control_lane"]}, {"name": "peek_and_fire", "priority": 8, "cooldown": 0.3, "conditions": ["enemy_behind_cover", "has_cover_position", "good_angle"], "actions": ["peek_left_or_right", "quick_burst_fire", "return_to_cover", "reposition_if_needed"]}]}, "loadouts": {"assault": {"primary_weapon": "AR_M416", "secondary_weapon": "SMG_UMP45", "preferred_range": "medium", "playstyle": "aggressive_balanced"}, "support": {"primary_weapon": "AR_SCAR", "secondary_weapon": "SMG_VECTOR", "preferred_range": "medium_long", "playstyle": "supportive_covering"}, "sniper": {"primary_weapon": "SNIPER_KAR98", "secondary_weapon": "AR_AKM", "preferred_range": "long", "playstyle": "defensive_overwatch"}, "flanker": {"primary_weapon": "SMG_UZI", "secondary_weapon": "SHOTGUN_S12K", "preferred_range": "close", "playstyle": "aggressive_mobile"}, "entry": {"primary_weapon": "SHOTGUN_S12K", "secondary_weapon": "SMG_VECTOR", "preferred_range": "close", "playstyle": "aggressive_entry"}}, "weapon_stats": {"AR_M416": {"damage": 43, "fire_rate": 700, "range": 500, "accuracy": 85, "stability": 80, "magazine_size": 30, "reload_time": 2.3, "recoil_vertical": 15, "recoil_horizontal": 8}, "AR_AKM": {"damage": 49, "fire_rate": 600, "range": 450, "accuracy": 75, "stability": 70, "magazine_size": 30, "reload_time": 2.5, "recoil_vertical": 20, "recoil_horizontal": 12}, "AR_SCAR": {"damage": 41, "fire_rate": 650, "range": 480, "accuracy": 80, "stability": 85, "magazine_size": 30, "reload_time": 2.2, "recoil_vertical": 12, "recoil_horizontal": 6}, "SMG_UMP45": {"damage": 35, "fire_rate": 600, "range": 200, "accuracy": 70, "stability": 90, "magazine_size": 25, "reload_time": 2.0, "recoil_vertical": 8, "recoil_horizontal": 10}, "SMG_UZI": {"damage": 26, "fire_rate": 1200, "range": 150, "accuracy": 65, "stability": 75, "magazine_size": 25, "reload_time": 1.8, "recoil_vertical": 18, "recoil_horizontal": 15}, "SMG_VECTOR": {"damage": 31, "fire_rate": 1200, "range": 180, "accuracy": 75, "stability": 80, "magazine_size": 19, "reload_time": 1.9, "recoil_vertical": 20, "recoil_horizontal": 12}, "SHOTGUN_S12K": {"damage": 24, "fire_rate": 300, "range": 50, "accuracy": 60, "stability": 85, "magazine_size": 5, "reload_time": 3.5, "recoil_vertical": 25, "recoil_horizontal": 20}, "SNIPER_KAR98": {"damage": 79, "fire_rate": 60, "range": 800, "accuracy": 95, "stability": 90, "magazine_size": 5, "reload_time": 3.8, "recoil_vertical": 35, "recoil_horizontal": 5}, "SNIPER_SKS": {"damage": 53, "fire_rate": 200, "range": 600, "accuracy": 90, "stability": 85, "magazine_size": 10, "reload_time": 2.8, "recoil_vertical": 25, "recoil_horizontal": 8}, "SNIPER_M24": {"damage": 84, "fire_rate": 50, "range": 850, "accuracy": 98, "stability": 95, "magazine_size": 5, "reload_time": 4.0, "recoil_vertical": 40, "recoil_horizontal": 3}}, "tactical_parameters": {"engagement_range": {"close": 100, "medium": 300, "long": 600, "sniper": 1000}, "reaction_times": {"immediate_threat": 0.1, "enemy_spotted": 0.3, "tactical_decision": 0.5, "positioning": 1.0}, "team_coordination": {"communication_range": 500, "support_distance": 200, "flank_coordination_time": 3.0, "revive_priority_distance": 150}, "movement_speeds": {"walk": 150, "run": 300, "crouch": 100, "prone": 50, "slide": 400}, "combat_parameters": {"peek_duration": 1.5, "suppression_duration": 3.0, "reload_cover_seeking": true, "auto_lean": true, "recoil_compensation": 0.8}}, "map_specific": {"warehouse_tdm": {"lanes": ["left", "center", "right"], "key_positions": [{"name": "center_control", "location": [0, 0, 100], "importance": "high", "roles": ["assault", "support"]}, {"name": "left_flank", "location": [-500, 200, 100], "importance": "medium", "roles": ["flanker", "entry"]}, {"name": "right_flank", "location": [500, 200, 100], "importance": "medium", "roles": ["flanker", "entry"]}, {"name": "sniper_overwatch", "location": [0, -400, 150], "importance": "high", "roles": ["sniper"]}], "spawn_protection_zones": [{"team": 0, "location": [0, -800, 100], "radius": 200}, {"team": 1, "location": [0, 800, 100], "radius": 200}]}}}}