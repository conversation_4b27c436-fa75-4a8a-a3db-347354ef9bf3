# 📊 Complete DataTable Setup Guide for TDM AI System

## 🎯 Overview

This guide provides the complete setup for all DataTables needed for the PUBGM TDM AI system. Follow these steps to create a fully functional data-driven AI system.

## 📋 Required DataTables

### 1. **DT_WeaponStats** - Weapon Statistics
- **Purpose**: PUBGM-accurate weapon data
- **Row Type**: DataTableRowBase
- **Location**: Content/Data/DataTables/
- **Entries**: 10 weapons (AR, SMG, Shotgun, Sniper)

### 2. **DT_RoleLoadouts** - Role-Based Loadouts
- **Purpose**: Team role configurations
- **Row Type**: DataTableRowBase
- **Location**: Content/Data/DataTables/
- **Entries**: 5 primary roles + 3 alternatives

### 3. **DT_AIDecisions** - AI Decision Tree
- **Purpose**: Tactical decision making
- **Row Type**: DataTableRowBase
- **Location**: Content/Data/DataTables/
- **Entries**: 8 priority levels + role-specific decisions

### 4. **DT_MapPositions** - Map Positioning
- **Purpose**: Spawn points and strategic positions
- **Row Type**: DataTableRowBase
- **Location**: Content/Data/DataTables/
- **Entries**: 20+ positions for warehouse map

### 5. **DT_TacticalParameters** - System Parameters
- **Purpose**: Fine-tuning AI behavior
- **Row Type**: DataTableRowBase
- **Location**: Content/Data/DataTables/
- **Entries**: 30+ configurable parameters

## 🚀 Quick Setup Process

### Step 1: Create Folder Structure
```
Content/
└── Data/
    └── DataTables/
        ├── DT_WeaponStats.uasset
        ├── DT_RoleLoadouts.uasset
        ├── DT_AIDecisions.uasset
        ├── DT_MapPositions.uasset
        └── DT_TacticalParameters.uasset
```

### Step 2: Create Each DataTable

#### For Each DataTable:
1. **Right-click in Content/Data/DataTables/**
2. **Miscellaneous → Data Table**
3. **Choose Row Structure: `DataTableRowBase`**
4. **Name according to list above**
5. **Follow specific setup guide for each table**

### Step 3: Import Data

#### Option A: Manual Entry (Recommended)
- Follow the detailed setup guides for each DataTable
- Copy the provided data entries exactly
- Verify all column types match specifications

#### Option B: CSV Import (Advanced)
- Export the manual entries to CSV format
- Use UE5's CSV import functionality
- Validate data integrity after import

## 🔧 DataTable Integration in Blueprints

### Blueprint Setup for AI Controller

#### Create BP_TDMAIController:
```blueprint
// Variables
- WeaponStatsTable: Data Table Reference (DT_WeaponStats)
- RoleLoadoutsTable: Data Table Reference (DT_RoleLoadouts)
- AIDecisionsTable: Data Table Reference (DT_AIDecisions)
- MapPositionsTable: Data Table Reference (DT_MapPositions)
- TacticalParametersTable: Data Table Reference (DT_TacticalParameters)

// Functions
- GetWeaponStats(WeaponName: String): WeaponStats Struct
- GetRoleLoadout(RoleName: String): RoleLoadout Struct
- EvaluateAIDecision(): Decision Struct
- GetSpawnPosition(TeamID: Integer): Vector
- GetTacticalParameter(ParameterName: String): Float
```

### Usage Examples

#### Get Weapon Statistics:
```blueprint
Event BeginPlay
├── Get Data Table Row
│   ├── Data Table: DT_WeaponStats
│   ├── Row Name: "AR_M416"
│   └── Out Row: WeaponStats
├── Set Weapon Damage: WeaponStats.Damage
├── Set Fire Rate: WeaponStats.FireRate
└── Configure Weapon System
```

#### Role Assignment:
```blueprint
Function AssignRole
├── Get Data Table Row
│   ├── Data Table: DT_RoleLoadouts
│   ├── Row Name: [Selected Role]
│   └── Out Row: RoleLoadout
├── Set Primary Weapon: RoleLoadout.PrimaryWeapon
├── Set Secondary Weapon: RoleLoadout.SecondaryWeapon
├── Set Aggression Level: RoleLoadout.AggressionLevel
└── Apply Role Configuration
```

#### AI Decision Making:
```blueprint
Function MakeDecision
├── Get Data Table Rows (DT_AIDecisions)
├── Sort by Priority
├── For Each Decision
│   ├── Check Cooldown
│   ├── Evaluate Conditions
│   ├── If All Conditions Met
│   │   ├── Execute Actions
│   │   ├── Set Cooldown
│   │   └── Return Decision
│   └── Continue to Next
└── Return Default Decision
```

#### Spawn Position Selection:
```blueprint
Function GetSpawnPosition
├── Get Data Table Rows (DT_MapPositions)
├── Filter: IsSpawnPoint = true AND TeamID = [TeamID]
├── Select Random from Filtered Results
└── Return Position Vector
```

#### Parameter Access:
```blueprint
Function GetParameter
├── Get Data Table Row
│   ├── Data Table: DT_TacticalParameters
│   ├── Row Name: [ParameterName]
│   └── Out Row: Parameter
├── Switch on ValueType
│   ├── "Float": Return FloatValue
│   ├── "Integer": Return IntValue
│   ├── "Boolean": Return BoolValue
│   └── "String": Return StringValue
```

## 🎮 Game Mode Integration

### BP_TDMGameMode Setup:
```blueprint
// Variables
- AllDataTables: Array of Data Table References

// Event BeginPlay
├── Initialize All DataTables
├── Validate Data Integrity
├── Configure Match Parameters from DT_TacticalParameters
├── Setup Team Compositions from DT_RoleLoadouts
└── Initialize Spawn System from DT_MapPositions
```

## 🔍 Data Validation

### Validation Checklist:
- [ ] All DataTables created with correct names
- [ ] All required columns added with correct types
- [ ] All data entries populated accurately
- [ ] No missing or null values in critical fields
- [ ] Cross-references between tables are valid
- [ ] Blueprint references to DataTables are set
- [ ] Test data access in Play mode

### Common Issues and Solutions:

#### DataTable Not Found:
```
Problem: Blueprint can't find DataTable
Solution: Check file path and ensure DataTable is in correct folder
```

#### Wrong Data Type:
```
Problem: Column shows wrong data type
Solution: Recreate column with correct type, re-enter data
```

#### Missing Entries:
```
Problem: Some weapons/roles not working
Solution: Verify all entries from setup guides are present
```

#### Performance Issues:
```
Problem: Slow DataTable access
Solution: Cache frequently accessed data in variables
```

## 📈 Performance Optimization

### Best Practices:
1. **Cache Data**: Store frequently accessed rows in variables
2. **Batch Operations**: Group multiple DataTable accesses
3. **Lazy Loading**: Load data only when needed
4. **Memory Management**: Clear unused DataTable references

### Optimization Example:
```blueprint
// Cache weapon stats at startup
Event BeginPlay
├── For Each Weapon Type
│   ├── Get Data Table Row (DT_WeaponStats)
│   ├── Store in WeaponStatsCache Map
│   └── Continue
└── Use Cache for Runtime Access
```

## 🧪 Testing and Debugging

### Debug Tools:
```blueprint
// DataTable Debug Function
Function DebugDataTables
├── Print All Weapon Stats
├── Print All Role Loadouts
├── Print All AI Decisions
├── Print All Map Positions
└── Print All Parameters
```

### Test Scenarios:
1. **Weapon Switching**: Verify stats change correctly
2. **Role Assignment**: Check loadout application
3. **AI Decisions**: Monitor decision tree execution
4. **Spawn System**: Test all spawn points
5. **Parameter Tuning**: Modify values and observe changes

## 🎯 Production Deployment

### Final Steps:
1. **Package DataTables**: Ensure all tables are included in build
2. **Version Control**: Commit all DataTable files
3. **Documentation**: Update any game design documents
4. **Backup**: Create backup copies of all DataTables
5. **Testing**: Full system test in packaged build

### Maintenance:
- **Regular Updates**: Keep weapon stats current with PUBGM updates
- **Balance Adjustments**: Tune parameters based on gameplay testing
- **New Content**: Add new weapons, roles, or maps as needed
- **Performance Monitoring**: Track DataTable access performance

This complete setup provides a robust, data-driven foundation for the PUBGM TDM AI system with easy maintenance and modification capabilities.
