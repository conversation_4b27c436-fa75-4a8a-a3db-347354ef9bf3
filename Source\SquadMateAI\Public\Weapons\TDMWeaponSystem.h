#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "AI/TDMAIController.h"
#include "TDMWeaponSystem.generated.h"

// Weapon Statistics (PUBGM accurate)
USTRUCT(BlueprintType)
struct FTDMWeaponStats
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    ETDMWeaponType WeaponType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString WeaponName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float Damage;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float FireRate; // Rounds per minute

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float Range;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float Accuracy;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float Stability;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 MagazineSize;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float ReloadTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float RecoilVertical;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float RecoilHorizontal;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIsAutomatic;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bCanADS; // Aim Down Sights

    FTDMWeaponStats()
    {
        WeaponType = ETDMWeaponType::AR_M416;
        WeaponName = TEXT("M416");
        Damage = 43.0f;
        FireRate = 700.0f;
        Range = 500.0f;
        Accuracy = 85.0f;
        Stability = 80.0f;
        MagazineSize = 30;
        ReloadTime = 2.3f;
        RecoilVertical = 15.0f;
        RecoilHorizontal = 8.0f;
        bIsAutomatic = true;
        bCanADS = true;
    }
};

// Fire Mode
UENUM(BlueprintType)
enum class ETDMFireMode : uint8
{
    Single      UMETA(DisplayName = "Single"),
    Burst       UMETA(DisplayName = "Burst"),
    Auto        UMETA(DisplayName = "Auto")
};

// Aim Mode
UENUM(BlueprintType)
enum class ETDMAimMode : uint8
{
    Hipfire     UMETA(DisplayName = "Hipfire"),
    ADS         UMETA(DisplayName = "ADS"),
    Scope       UMETA(DisplayName = "Scope")
};

// Weapon Events
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnWeaponFired, ETDMWeaponType, WeaponType);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnWeaponReloaded, ETDMWeaponType, WeaponType);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnWeaponSwitched, ETDMWeaponType, OldWeapon, ETDMWeaponType, NewWeapon);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAmmoChanged, int32, CurrentAmmo);

/**
 * TDM Weapon System - Handles PUBG Mobile weapon mechanics for AI characters
 */
UCLASS(BlueprintType, Blueprintable, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class SQUADMATEAI_API UTDMWeaponSystem : public UActorComponent
{
    GENERATED_BODY()

public:
    UTDMWeaponSystem();

protected:
    virtual void BeginPlay() override;
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

public:
    // Current Weapon State
    UPROPERTY(BlueprintReadOnly, Category = "Weapon State")
    ETDMWeaponType CurrentPrimaryWeapon;

    UPROPERTY(BlueprintReadOnly, Category = "Weapon State")
    ETDMWeaponType CurrentSecondaryWeapon;

    UPROPERTY(BlueprintReadOnly, Category = "Weapon State")
    ETDMWeaponType ActiveWeapon;

    UPROPERTY(BlueprintReadOnly, Category = "Weapon State")
    int32 CurrentAmmo;

    UPROPERTY(BlueprintReadOnly, Category = "Weapon State")
    int32 ReserveAmmo;

    UPROPERTY(BlueprintReadOnly, Category = "Weapon State")
    bool bIsFiring;

    UPROPERTY(BlueprintReadOnly, Category = "Weapon State")
    bool bIsReloading;

    UPROPERTY(BlueprintReadOnly, Category = "Weapon State")
    bool bIsAiming;

    UPROPERTY(BlueprintReadOnly, Category = "Weapon State")
    ETDMFireMode CurrentFireMode;

    UPROPERTY(BlueprintReadOnly, Category = "Weapon State")
    ETDMAimMode CurrentAimMode;

    // Weapon Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Configuration")
    TMap<ETDMWeaponType, FTDMWeaponStats> WeaponDatabase;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Configuration")
    bool bInfiniteAmmo; // TDM has infinite ammo

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Configuration")
    bool bAutoReload;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Configuration")
    bool bRecoilControl;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Configuration")
    float AimAssistStrength;

    // Events
    UPROPERTY(BlueprintAssignable, Category = "Weapon Events")
    FOnWeaponFired OnWeaponFired;

    UPROPERTY(BlueprintAssignable, Category = "Weapon Events")
    FOnWeaponReloaded OnWeaponReloaded;

    UPROPERTY(BlueprintAssignable, Category = "Weapon Events")
    FOnWeaponSwitched OnWeaponSwitched;

    UPROPERTY(BlueprintAssignable, Category = "Weapon Events")
    FOnAmmoChanged OnAmmoChanged;

    // Public Interface
    UFUNCTION(BlueprintCallable, Category = "TDM Weapon System")
    void InitializeForTDM();

    UFUNCTION(BlueprintCallable, Category = "TDM Weapon System")
    void SwitchLoadout(const FTDMLoadout& NewLoadout);

    UFUNCTION(BlueprintCallable, Category = "TDM Weapon System")
    void SwitchToPrimary();

    UFUNCTION(BlueprintCallable, Category = "TDM Weapon System")
    void SwitchToSecondary();

    UFUNCTION(BlueprintCallable, Category = "TDM Weapon System")
    void SwitchWeapon(ETDMWeaponType WeaponType);

    UFUNCTION(BlueprintCallable, Category = "TDM Weapon System")
    void StartFiring();

    UFUNCTION(BlueprintCallable, Category = "TDM Weapon System")
    void StopFiring();

    UFUNCTION(BlueprintCallable, Category = "TDM Weapon System")
    void StartReload();

    UFUNCTION(BlueprintCallable, Category = "TDM Weapon System")
    void StartAiming();

    UFUNCTION(BlueprintCallable, Category = "TDM Weapon System")
    void StopAiming();

    UFUNCTION(BlueprintCallable, Category = "TDM Weapon System")
    void SetFireMode(bool bAutoFire);

    UFUNCTION(BlueprintCallable, Category = "TDM Weapon System")
    void SetAimAssist(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "TDM Weapon System")
    void EnableRecoilControl(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "TDM Weapon System")
    void SetSuppressiveMode(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "TDM Weapon System")
    void RefreshAmmo();

    // Getters
    UFUNCTION(BlueprintPure, Category = "TDM Weapon System")
    FTDMWeaponStats GetCurrentWeaponStats() const;

    UFUNCTION(BlueprintPure, Category = "TDM Weapon System")
    FTDMWeaponStats GetWeaponStats(ETDMWeaponType WeaponType) const;

    UFUNCTION(BlueprintPure, Category = "TDM Weapon System")
    bool CanFire() const;

    UFUNCTION(BlueprintPure, Category = "TDM Weapon System")
    bool NeedsReload() const;

    UFUNCTION(BlueprintPure, Category = "TDM Weapon System")
    float GetAccuracy() const;

    UFUNCTION(BlueprintPure, Category = "TDM Weapon System")
    float GetDamage() const;

    UFUNCTION(BlueprintPure, Category = "TDM Weapon System")
    float GetRange() const;

    UFUNCTION(BlueprintPure, Category = "TDM Weapon System")
    FVector GetMuzzleLocation() const;

    UFUNCTION(BlueprintPure, Category = "TDM Weapon System")
    FVector GetAimDirection() const;

protected:
    // Internal State
    float LastFireTime;
    float ReloadStartTime;
    FTimerHandle FireTimerHandle;
    FTimerHandle ReloadTimerHandle;
    FVector RecoilOffset;
    bool bSuppressiveMode;

    // Internal Methods
    void InitializeWeaponDatabase();
    void SetupDefaultLoadout();
    void ProcessFiring();
    void ProcessRecoil();
    void ProcessAiming();
    void UpdateAmmoDisplay();

    // Firing Logic
    void FireSingleShot();
    void FireBurst();
    void FireAutomatic();
    bool CanFireNextShot() const;
    void ApplyRecoil();
    void ResetRecoil();

    // Reload Logic
    void CompleteReload();
    void CancelReload();

    // Weapon Switching
    void PerformWeaponSwitch(ETDMWeaponType NewWeapon);
    void UpdateWeaponMesh();

    // Aim Assist
    void ApplyAimAssist();
    AActor* FindBestTarget() const;
    FVector CalculateAimAssistDirection(AActor* Target) const;

    // Recoil Control
    void ApplyRecoilControl();
    FVector CalculateRecoilPattern() const;
    void CompensateRecoil();

    // Utility
    float GetFireInterval() const;
    bool IsWeaponAutomatic() const;
    void PlayFireEffects();
    void PlayReloadEffects();
};
