# 🎮 TDM Production AI Deployment Guide

## 📋 Overview

This guide provides step-by-step instructions for deploying the production-ready AI agent system for 5v5 Team Deathmatch matches using PUBG Mobile mechanics with Victor 3D models.

## 🏗️ System Architecture

### Core Components Created:
- **ATDMAIController**: Enhanced AI controller with PUBGM TDM mechanics
- **ATDMGameMode**: 5v5 match management with scoring and respawn systems
- **AVictorCharacter**: Victor 3D model integration with PUBGM movement
- **UTDMWeaponSystem**: Accurate PUBGM weapon mechanics and loadouts
- **JSON Configuration**: Dynamic decision tree and tactical parameters

## 🚀 Quick Start Deployment

### 1. Project Setup
```bash
# Ensure UE 5.6 is installed
# Open AISquadMate.uproject in Unreal Engine 5.6
```

### 2. Asset Integration

#### Victor 3D Model Setup:
1. **Import Victor Model**:
   - Source: `victor/Victore model fix.fbx`
   - Target: `Content/Characters/Victor/`
   - Import as Skeletal Mesh with animations

2. **Apply Victor Textures**:
   - `victor/textures/mat0_baseColor.png` → `Content/Characters/Victor/Materials/`
   - `victor/textures/mat0.001_baseColor.png` → `Content/Characters/Victor/Materials/`
   - `victor/textures/mat0.002_baseColor.png` → `Content/Characters/Victor/Materials/`

3. **Create Materials**:
   - Create `M_Victor_mat0`, `M_Victor_mat0_001`, `M_Victor_mat0_002`
   - Apply base color textures to respective materials

#### TDM Map Setup:
1. **Import TDM Map**:
   - Source: `tdm/source/Tdm.fbx`
   - Target: `Content/Maps/TDM/`
   - Set up as level geometry

2. **Configure Spawn Points**:
   - Place PlayerStart actors for Team 0 (Blue) and Team 1 (Red)
   - Ensure spawn points are in protected areas

### 3. Blueprint Configuration

#### Create Victor Character Blueprint:
1. **Create BP_VictorCharacter**:
   - Parent Class: `VictorCharacter`
   - Location: `Content/Characters/Victor/`

2. **Configure Components**:
   ```cpp
   // In BP_VictorCharacter
   - Set Skeletal Mesh to Victor model
   - Apply Victor materials
   - Configure collision capsule
   - Set up weapon attachment sockets
   ```

3. **Animation Blueprint**:
   - Create `ABP_Victor` based on Victor skeletal mesh
   - Implement state machine for TDM animations
   - Add variables for movement and combat states

#### Create TDM Game Mode Blueprint:
1. **Create BP_TDMGameMode**:
   - Parent Class: `TDMGameMode`
   - Location: `Content/GameModes/`

2. **Configure Settings**:
   ```cpp
   // Default settings
   - Default Pawn Class: BP_VictorCharacter
   - Player Controller Class: TDMAIController
   - Kill Limit: 40
   - Time Limit: 600 seconds (10 minutes)
   - Respawn Delay: 3 seconds
   ```

### 4. AI Configuration

#### Behavior Tree Setup:
1. **Create BT_TDM_Enhanced**:
   - Location: `Content/AI/BehaviorTrees/`
   - Import existing BT structure from project

2. **Blackboard Setup**:
   - Create `BB_TDM_Enhanced`
   - Configure keys for TDM-specific variables

#### JSON Configuration:
1. **Verify JSON Config**:
   - File: `Content/AI/Configurations/TDM_AI_Config.json`
   - Contains decision trees, weapon stats, and tactical parameters

## 🎯 Production Configuration

### Weapon System Configuration

#### PUBGM Accurate Weapon Stats:
```json
{
  "AR_M416": {
    "damage": 43,
    "fire_rate": 700,
    "accuracy": 85,
    "stability": 80
  },
  "SMG_UMP45": {
    "damage": 35,
    "fire_rate": 600,
    "accuracy": 70,
    "stability": 90
  }
  // ... additional weapons
}
```

#### Loadout Assignments:
- **Assault**: M416 + UMP45
- **Support**: SCAR-L + Vector
- **Sniper**: Kar98 + AKM
- **Flanker**: UZI + S12K
- **Entry**: S12K + Vector

### AI Tactical Configuration

#### Decision Tree Priorities:
1. **Immediate Threat Response** (Priority 1)
2. **Revive Priority** (Priority 2)
3. **Aggressive Engagement** (Priority 3)
4. **Flanking Maneuver** (Priority 4)
5. **Suppressive Fire** (Priority 5)
6. **Defensive Retreat** (Priority 6)
7. **Tactical Positioning** (Priority 7)
8. **Peek and Fire** (Priority 8)

#### Role-Based Behavior:
- **Assault**: Balanced aggression, center lane control
- **Support**: Covering fire, team coordination
- **Sniper**: Long-range overwatch, back positioning
- **Flanker**: Side lane control, mobility focus
- **Entry**: Close-quarters combat, aggressive pushing

## 🎮 Match Flow

### Match Initialization:
1. **Team Setup**: 2 teams of 5 AI players each
2. **Role Assignment**: Automatic role distribution per team
3. **Spawn Protection**: 3-second invulnerability after spawn
4. **Loadout Application**: Role-based weapon assignments

### Win Conditions:
- **Kill Limit**: First team to 40 kills wins
- **Time Limit**: Highest score after 10 minutes wins
- **Overtime**: If tied, sudden death mode

### Respawn System:
- **Respawn Delay**: 3 seconds after death
- **Spawn Protection**: 3 seconds of invulnerability
- **Loadout Refresh**: Full ammo and health restoration

## 🔧 Performance Optimization

### AI Performance:
- **Decision Frequency**: 0.1 seconds for combat decisions
- **Perception Range**: 3000 units sight, 2000 units hearing
- **LOD System**: Distance-based AI complexity reduction

### Rendering Optimization:
- **Victor Model LOD**: 3 levels of detail
- **Animation LOD**: Reduced update frequency at distance
- **Culling**: Frustum and distance-based culling

## 🧪 Testing Procedures

### AI Behavior Testing:
1. **Combat Effectiveness**:
   ```bash
   # Test commands in console
   TDM.TestCombat 1
   TDM.TestFlanking 1
   TDM.TestRevive 1
   ```

2. **Performance Metrics**:
   - K/D ratios per role
   - Accuracy percentages
   - Tactical efficiency scores

3. **Team Coordination**:
   - Lane control effectiveness
   - Revive success rates
   - Communication timing

### Match Flow Testing:
1. **Start Match**: Verify 5v5 spawn and initialization
2. **Mid-Match**: Test respawn, scoring, and coordination
3. **End Conditions**: Verify win condition detection

## 📊 Analytics and Monitoring

### Performance Metrics:
- **Individual Stats**: Kills, Deaths, Accuracy, Efficiency
- **Team Stats**: Total kills, lane control, coordination
- **Match Stats**: Duration, win conditions, balance

### Debug Tools:
- **AI Debug Display**: Show decision states and targets
- **Performance Profiler**: Monitor AI computation costs
- **Behavior Tree Debugger**: Visualize AI decision flow

## 🚨 Troubleshooting

### Common Issues:

#### AI Not Spawning:
```cpp
// Check in TDMGameMode::SpawnAIPlayers()
- Verify VictorCharacterClass is set
- Ensure TDMAIControllerClass is assigned
- Check spawn point placement
```

#### Weapons Not Working:
```cpp
// Check in UTDMWeaponSystem::InitializeForTDM()
- Verify weapon database initialization
- Check infinite ammo setting
- Ensure weapon mesh attachment
```

#### Animation Issues:
```cpp
// Check in AVictorCharacter::SetupVictorMesh()
- Verify Victor model import
- Check animation blueprint assignment
- Ensure material application
```

## 🎯 Production Checklist

### Pre-Deployment:
- [ ] Victor 3D model imported and textured
- [ ] TDM map imported and configured
- [ ] All AI components compiled successfully
- [ ] JSON configuration validated
- [ ] Behavior trees and blackboards set up
- [ ] Weapon system configured with PUBGM stats

### Testing Phase:
- [ ] 5v5 match starts correctly
- [ ] AI players spawn with proper roles
- [ ] Combat mechanics work as expected
- [ ] Respawn system functions properly
- [ ] Win conditions trigger correctly
- [ ] Performance meets target FPS

### Production Ready:
- [ ] All systems integrated and tested
- [ ] Performance optimized
- [ ] Analytics and monitoring active
- [ ] Documentation complete
- [ ] Backup and version control in place

## 🎮 Usage Instructions

### Starting a TDM Match:
1. Open the TDM level in UE5 editor
2. Set Game Mode to `BP_TDMGameMode`
3. Click Play to start 5v5 AI match
4. Observe AI behavior and match progression

### Customizing AI Behavior:
1. Edit `Content/AI/Configurations/TDM_AI_Config.json`
2. Modify decision tree priorities and conditions
3. Adjust weapon stats and loadouts
4. Reload configuration in-game

### Monitoring Performance:
1. Use `stat AI` console command for AI performance
2. Use `stat FPS` for frame rate monitoring
3. Enable AI debug display for behavior visualization

## 📈 Future Enhancements

### Planned Features:
- **Advanced Team Tactics**: Coordinated strategies
- **Dynamic Difficulty**: Adaptive AI skill levels
- **Map Variations**: Multiple TDM environments
- **Spectator Mode**: Match observation tools
- **Replay System**: Match recording and analysis

This production-ready system provides a solid foundation for 5v5 TDM AI matches with authentic PUBG Mobile mechanics and the Victor character model integration.
