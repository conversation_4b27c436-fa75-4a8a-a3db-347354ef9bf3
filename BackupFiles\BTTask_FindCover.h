#pragma once

#include "CoreMinimal.h"
#include "BehaviorTree/BTTaskNode.h"
#include "EnvironmentQuery/EnvQueryTypes.h"
#include "BTTask_FindCover.generated.h"

UENUM(BlueprintType)
enum class ECoverType : uint8
{
    Any             UMETA(DisplayName = "Any Cover"),
    HighCover       UMETA(DisplayName = "High Cover"),
    LowCover        UMETA(DisplayName = "Low Cover"),
    PartialCover    UMETA(DisplayName = "Partial Cover"),
    FullCover       UMETA(DisplayName = "Full Cover")
};

UENUM(BlueprintType)
enum class ECoverSearchMode : uint8
{
    Nearest         UMETA(DisplayName = "Nearest"),
    Safest          UMETA(DisplayName = "Safest"),
    Tactical        UMETA(DisplayName = "Tactical"),
    Flanking        UMETA(DisplayName = "Flanking")
};

/**
 * Behavior Tree task that finds suitable cover positions using EQS queries
 * Considers enemy positions, ally locations, and tactical advantages
 */
UCLASS(BlueprintType, meta=(DisplayName="Find Cover"))
class SQUADMATEAI_API UBTTask_FindCover : public UBTTaskNode
{
    GENERATED_BODY()

public:
    UBTTask_FindCover();

protected:
    virtual EBTNodeResult::Type ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;
    virtual void OnTaskFinished(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EBTNodeResult::Type TaskResult) override;
    virtual FString GetStaticDescription() const override;

    // EQS Query for finding cover points
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS")
    class UEnvQuery* CoverQuery;

    // Blackboard key to store the found cover location
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector CoverLocationKey;

    // Blackboard key for enemy target (used to find cover relative to enemy)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector EnemyTargetKey;

    // Type of cover to search for
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cover")
    ECoverType PreferredCoverType = ECoverType::Any;

    // Search mode for cover selection
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cover")
    ECoverSearchMode SearchMode = ECoverSearchMode::Nearest;

    // Maximum distance to search for cover
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cover", meta = (ClampMin = "100.0"))
    float MaxSearchDistance = 1500.0f;

    // Minimum distance from enemy to be considered safe cover
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cover", meta = (ClampMin = "50.0"))
    float MinDistanceFromEnemy = 300.0f;

    // Whether to prefer cover that allows line of sight to enemy
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cover")
    bool bPreferOffensiveCover = true;

    // Whether to consider ally positions when selecting cover
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cover")
    bool bConsiderAllyPositions = true;

    // Minimum distance from allies to avoid clustering
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cover", meta = (ClampMin = "50.0"))
    float MinDistanceFromAllies = 200.0f;

    // Time limit for the EQS query
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "0.1"))
    float QueryTimeLimit = 2.0f;

    // Whether to move to cover immediately after finding it
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    bool bMoveToFoundCover = true;

    // Movement speed when going to cover
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    float MovementSpeed = 600.0f;

    // Whether to use crouched movement to cover
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    bool bUseCrouchedMovement = false;

    // Debug options
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bDrawDebugInfo = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    float DebugDrawDuration = 5.0f;

public:
    // Static utility functions for cover evaluation
    UFUNCTION(BlueprintCallable, Category = "Cover", CallInEditor = true)
    static bool IsCoverValid(const FVector& CoverLocation, const FVector& EnemyLocation, 
                           const FVector& AgentLocation, float MinDistance = 300.0f);

    UFUNCTION(BlueprintCallable, Category = "Cover", CallInEditor = true)
    static float EvaluateCoverQuality(const FVector& CoverLocation, const FVector& EnemyLocation, 
                                    const FVector& AgentLocation, ECoverType CoverType = ECoverType::Any);

    UFUNCTION(BlueprintCallable, Category = "Cover", CallInEditor = true)
    static bool HasLineOfSightFromCover(const FVector& CoverLocation, const FVector& TargetLocation, 
                                      UWorld* World, float CoverHeight = 180.0f);

protected:
    // EQS Query handling
    void StartCoverQuery(UBehaviorTreeComponent& OwnerComp);
    void OnEQSQueryComplete(TSharedPtr<FEnvQueryResult> Result, UBehaviorTreeComponent* OwnerComp);

    // Cover evaluation
    FVector SelectBestCoverLocation(const TArray<FVector>& CoverLocations, 
                                  const FVector& AgentLocation, const FVector& EnemyLocation,
                                  const TArray<FVector>& AllyLocations);

    float ScoreCoverLocation(const FVector& CoverLocation, const FVector& AgentLocation, 
                           const FVector& EnemyLocation, const TArray<FVector>& AllyLocations);

    // Utility functions
    bool IsLocationSafeFromEnemies(const FVector& Location, const TArray<FVector>& EnemyLocations);
    bool IsLocationTooCloseToAllies(const FVector& Location, const TArray<FVector>& AllyLocations);
    float CalculateTacticalAdvantage(const FVector& CoverLocation, const FVector& EnemyLocation);

    // Movement handling
    void MoveToCover(UBehaviorTreeComponent& OwnerComp, const FVector& CoverLocation);
    void OnMoveToLocationComplete(UBehaviorTreeComponent& OwnerComp, bool bSuccess);

    // Debug visualization
    void DrawDebugCoverInfo(UWorld* World, const FVector& CoverLocation, const FVector& EnemyLocation, 
                          float CoverScore, bool bIsSelected = false);

private:
    // Task memory structure
    struct FBTTask_FindCoverMemory
    {
        TWeakObjectPtr<UBehaviorTreeComponent> OwnerComponent;
        FEnvQueryRequest QueryRequest;
        bool bQueryInProgress = false;
        bool bMovementInProgress = false;
        FVector SelectedCoverLocation = FVector::ZeroVector;
        float TaskStartTime = 0.0f;
    };

    // Internal state management
    void InitializeTaskMemory(uint8* NodeMemory);
    void CleanupTaskMemory(uint8* NodeMemory);
    FBTTask_FindCoverMemory* GetTaskMemory(uint8* NodeMemory);

    // Query parameter setup
    void SetupQueryParams(FEnvQueryRequest& QueryRequest, const FVector& AgentLocation, 
                         const FVector& EnemyLocation);

    // Cover type specific evaluation
    bool MeetsCoverTypeRequirements(const FVector& CoverLocation, const FVector& EnemyLocation, 
                                  ECoverType RequiredType);

    // Performance optimization
    bool ShouldSkipQuery(UBehaviorTreeComponent& OwnerComp);
    void CacheQueryResults(const TArray<FVector>& Results);
    
    // Cached results for performance
    static TMap<FVector, TArray<FVector>> CachedCoverResults;
    static float LastCacheTime;
    static const float CacheValidityDuration;
};
