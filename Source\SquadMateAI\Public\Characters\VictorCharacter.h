#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Character.h"
#include "AI/TDMAIController.h"
#include "Components/HealthComponent.h"
#include "Components/InventoryComponent.h"
#include "Components/SquadRoleComponent.h"
#include "VictorCharacter.generated.h"

class UTDMWeaponSystem;
class UReviveComponent;
class UDecisionLoggerComponent;

// Animation States for Victor
UENUM(BlueprintType)
enum class EVictorAnimationState : uint8
{
    Idle            UMETA(DisplayName = "Idle"),
    Walking         UMETA(DisplayName = "Walking"),
    Running         UMETA(DisplayName = "Running"),
    Crouching       UMETA(DisplayName = "Crouching"),
    Prone           UMETA(DisplayName = "Prone"),
    Aiming          UMETA(DisplayName = "Aiming"),
    Firing          UMETA(DisplayName = "Firing"),
    Reloading       UMETA(DisplayName = "Reloading"),
    PeekLeft        UMETA(DisplayName = "Peek Left"),
    PeekRight       UMETA(DisplayName = "Peek Right"),
    Sliding         UMETA(DisplayName = "Sliding"),
    Reviving        UMETA(DisplayName = "Reviving"),
    BeingRevived    UMETA(DisplayName = "Being Revived"),
    Down            UMETA(DisplayName = "Down"),
    Dead            UMETA(DisplayName = "Dead")
};

// Movement States
UENUM(BlueprintType)
enum class EVictorMovementState : uint8
{
    Standing        UMETA(DisplayName = "Standing"),
    Crouched        UMETA(DisplayName = "Crouched"),
    Prone           UMETA(DisplayName = "Prone"),
    Sliding         UMETA(DisplayName = "Sliding")
};

// Combat States
UENUM(BlueprintType)
enum class EVictorCombatState : uint8
{
    Passive         UMETA(DisplayName = "Passive"),
    Alert           UMETA(DisplayName = "Alert"),
    Combat          UMETA(DisplayName = "Combat"),
    Suppressed      UMETA(DisplayName = "Suppressed"),
    Flanking        UMETA(DisplayName = "Flanking")
};

/**
 * Victor Character - The main AI character for TDM matches
 * Integrates with Victor 3D model and implements PUBGM movement mechanics
 */
UCLASS(BlueprintType, Blueprintable)
class SQUADMATEAI_API AVictorCharacter : public ACharacter
{
    GENERATED_BODY()

public:
    AVictorCharacter();

protected:
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;
    virtual void SetupPlayerInputComponent(class UInputComponent* PlayerInputComponent) override;

public:
    // Core Components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Victor Components")
    UHealthComponent* HealthComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Victor Components")
    UInventoryComponent* InventoryComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Victor Components")
    USquadRoleComponent* SquadRoleComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Victor Components")
    UTDMWeaponSystem* WeaponSystem;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Victor Components")
    UReviveComponent* ReviveComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Victor Components")
    UDecisionLoggerComponent* DecisionLogger;

    // Victor Model Components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Victor Model")
    class USkeletalMeshComponent* VictorMesh;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Victor Model")
    class UStaticMeshComponent* WeaponMesh;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Victor Model")
    class UStaticMeshComponent* EquipmentMesh;

    // Animation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Victor Animation")
    class UAnimationBlueprint* VictorAnimBP;

    // Current States
    UPROPERTY(BlueprintReadOnly, Category = "Victor State")
    EVictorAnimationState CurrentAnimationState;

    UPROPERTY(BlueprintReadOnly, Category = "Victor State")
    EVictorMovementState CurrentMovementState;

    UPROPERTY(BlueprintReadOnly, Category = "Victor State")
    EVictorCombatState CurrentCombatState;

    UPROPERTY(BlueprintReadOnly, Category = "Victor State")
    int32 TeamID;

    // Movement Properties
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Victor Movement")
    float WalkSpeed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Victor Movement")
    float RunSpeed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Victor Movement")
    float CrouchSpeed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Victor Movement")
    float ProneSpeed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Victor Movement")
    float SlideSpeed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Victor Movement")
    float SlideDuration;

    // Combat Properties
    UPROPERTY(BlueprintReadOnly, Category = "Victor Combat")
    bool bIsAiming;

    UPROPERTY(BlueprintReadOnly, Category = "Victor Combat")
    bool bIsFiring;

    UPROPERTY(BlueprintReadOnly, Category = "Victor Combat")
    bool bIsReloading;

    UPROPERTY(BlueprintReadOnly, Category = "Victor Combat")
    bool bIsPeeking;

    UPROPERTY(BlueprintReadOnly, Category = "Victor Combat")
    bool bIsSuppressed;

    UPROPERTY(BlueprintReadOnly, Category = "Victor Combat")
    FRotator AimRotation;

    // Interaction Properties
    UPROPERTY(BlueprintReadOnly, Category = "Victor Interaction")
    bool bIsReviving;

    UPROPERTY(BlueprintReadOnly, Category = "Victor Interaction")
    bool bIsBeingRevived;

    UPROPERTY(BlueprintReadOnly, Category = "Victor Interaction")
    float ReviveProgress;

    // Public Interface
    UFUNCTION(BlueprintCallable, Category = "Victor Character")
    void SetAnimationState(EVictorAnimationState NewState);

    UFUNCTION(BlueprintCallable, Category = "Victor Character")
    void SetMovementState(EVictorMovementState NewState);

    UFUNCTION(BlueprintCallable, Category = "Victor Character")
    void SetCombatState(EVictorCombatState NewState);

    UFUNCTION(BlueprintCallable, Category = "Victor Character")
    void SetTeamID(int32 NewTeamID);

    // Movement Actions
    UFUNCTION(BlueprintCallable, Category = "Victor Movement")
    void StartCrouch();

    UFUNCTION(BlueprintCallable, Category = "Victor Movement")
    void StopCrouch();

    UFUNCTION(BlueprintCallable, Category = "Victor Movement")
    void StartProne();

    UFUNCTION(BlueprintCallable, Category = "Victor Movement")
    void StopProne();

    UFUNCTION(BlueprintCallable, Category = "Victor Movement")
    void StartSlide();

    UFUNCTION(BlueprintCallable, Category = "Victor Movement")
    void StopSlide();

    // Combat Actions
    UFUNCTION(BlueprintCallable, Category = "Victor Combat")
    void StartAiming();

    UFUNCTION(BlueprintCallable, Category = "Victor Combat")
    void StopAiming();

    UFUNCTION(BlueprintCallable, Category = "Victor Combat")
    void StartFiring();

    UFUNCTION(BlueprintCallable, Category = "Victor Combat")
    void StopFiring();

    UFUNCTION(BlueprintCallable, Category = "Victor Combat")
    void StartReload();

    UFUNCTION(BlueprintCallable, Category = "Victor Combat")
    void PeekLeft();

    UFUNCTION(BlueprintCallable, Category = "Victor Combat")
    void PeekRight();

    UFUNCTION(BlueprintCallable, Category = "Victor Combat")
    void StopPeeking();

    // Interaction Actions
    UFUNCTION(BlueprintCallable, Category = "Victor Interaction")
    void StartRevive(AVictorCharacter* Target);

    UFUNCTION(BlueprintCallable, Category = "Victor Interaction")
    void StopRevive();

    // Health Events
    UFUNCTION(BlueprintCallable, Category = "Victor Health")
    void OnTakeDamage(float Damage, AActor* DamageSource);

    UFUNCTION(BlueprintCallable, Category = "Victor Health")
    void OnDeath(AActor* Killer);

    UFUNCTION(BlueprintCallable, Category = "Victor Health")
    void OnRevived();

    UFUNCTION(BlueprintCallable, Category = "Victor Health")
    void OnRespawn();

    // Getters
    UFUNCTION(BlueprintPure, Category = "Victor Character")
    ATDMAIController* GetTDMAIController() const;

    UFUNCTION(BlueprintPure, Category = "Victor Character")
    bool IsAlive() const;

    UFUNCTION(BlueprintPure, Category = "Victor Character")
    bool IsDown() const;

    UFUNCTION(BlueprintPure, Category = "Victor Character")
    bool CanMove() const;

    UFUNCTION(BlueprintPure, Category = "Victor Character")
    bool CanFire() const;

    UFUNCTION(BlueprintPure, Category = "Victor Character")
    float GetHealthPercentage() const;

    UFUNCTION(BlueprintPure, Category = "Victor Character")
    FVector GetAimDirection() const;

protected:
    // Internal State
    float SlideStartTime;
    FVector SlideDirection;
    bool bIsSliding;
    AVictorCharacter* ReviveTarget;
    float ReviveStartTime;

    // Internal Methods
    void InitializeComponents();
    void SetupVictorMesh();
    void ConfigureMovement();
    void UpdateAnimationState();
    void UpdateMovementSpeed();
    void HandleSlideMovement(float DeltaTime);
    void HandleReviveProgress(float DeltaTime);
    void UpdateAimRotation();

    // Animation Helpers
    void PlayAnimationMontage(class UAnimMontage* Montage);
    void StopAnimationMontage();

    // Movement Helpers
    void ApplyMovementState();
    void TransitionToMovementState(EVictorMovementState NewState);

    // Combat Helpers
    void UpdateCombatState();
    void HandleSuppression();
    void ProcessAiming();

    // Team Identification
    void UpdateTeamVisuals();
    void SetTeamColor(const FLinearColor& Color);

    // Victor Model Integration
    void LoadVictorAssets();
    void ApplyVictorTextures();
    void SetupVictorAnimations();
};
