#pragma once

#include "CoreMinimal.h"
#include "AIController.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "Perception/AIPerceptionComponent.h"
#include "Perception/AISenseConfig_Sight.h"
#include "Perception/AISenseConfig_Hearing.h"
#include "GameplayTagContainer.h"
#include "SquadMateAIController.generated.h"

class ASquadManager;
class USquadRoleComponent;
class UDecisionLoggerComponent;

UENUM(BlueprintType)
enum class ETacticState : uint8
{
    Patrol      UMETA(DisplayName = "Patrol"),
    Engage      UMETA(DisplayName = "Engage"),
    Flank       UMETA(DisplayName = "Flank"),
    Retreat     UMETA(DisplayName = "Retreat"),
    Revive      UMETA(DisplayName = "Revive"),
    Hold        UMETA(DisplayName = "Hold"),
    Suppress    UMETA(DisplayName = "Suppress"),
    Peek        UMETA(DisplayName = "Peek")
};

UENUM(BlueprintType)
enum class ESquadRole : uint8
{
    Support     UMETA(DisplayName = "Support"),
    Assault     UMETA(DisplayName = "Assault"),
    Scout       UMETA(DisplayName = "Scout"),
    Anchor      UMETA(DisplayName = "Anchor"),
    Sniper      UMETA(DisplayName = "Sniper")
};

USTRUCT(BlueprintType)
struct FSquadMateStats
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 Kills = 0;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 Deaths = 0;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 Revives = 0;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 FlankSuccesses = 0;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float ZoneTime = 0.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float ELOScore = 1000.0f;

    FSquadMateStats()
    {
        Kills = 0;
        Deaths = 0;
        Revives = 0;
        FlankSuccesses = 0;
        ZoneTime = 0.0f;
        ELOScore = 1000.0f;
    }
};

UCLASS(BlueprintType, Blueprintable)
class SQUADMATEAI_API ASquadMateAIController : public AAIController
{
    GENERATED_BODY()

public:
    ASquadMateAIController();

protected:
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;
    virtual void OnPossess(APawn* InPawn) override;
    virtual void OnUnPossess() override;

    // AI Perception
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AI")
    class UAIPerceptionComponent* AIPerceptionComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AI")
    class UAISenseConfig_Sight* SightConfig;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AI")
    class UAISenseConfig_Hearing* HearingConfig;

    // Behavior Tree
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AI")
    class UBehaviorTree* BehaviorTree;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AI")
    class UBehaviorTreeComponent* BehaviorTreeComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AI")
    class UBlackboardComponent* BlackboardComponent;

    // Squad Management
    UPROPERTY(BlueprintReadWrite, Category = "Squad")
    ASquadManager* SquadManager;

    UPROPERTY(BlueprintReadWrite, Category = "Squad")
    ESquadRole AssignedRole;

    UPROPERTY(BlueprintReadWrite, Category = "Squad")
    ETacticState CurrentTactic;

    // Stats and Logging
    UPROPERTY(BlueprintReadWrite, Category = "Stats")
    FSquadMateStats AgentStats;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Logging")
    UDecisionLoggerComponent* DecisionLogger;

    // Blackboard Keys
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AI")
    FName TargetActorKey = "TargetActor";

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AI")
    FName TacticStateKey = "TacticState";

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AI")
    FName CoverLocationKey = "CoverLocation";

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AI")
    FName FlankLocationKey = "FlankLocation";

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AI")
    FName SquadRoleKey = "SquadRole";

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AI")
    FName AllyToReviveKey = "AllyToRevive";

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AI")
    FName LastKnownEnemyLocationKey = "LastKnownEnemyLocation";

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AI")
    FName IsInCombatKey = "IsInCombat";

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AI")
    FName HealthPercentageKey = "HealthPercentage";

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "AI")
    FName AmmoCountKey = "AmmoCount";

public:
    // Public Interface
    UFUNCTION(BlueprintCallable, Category = "AI")
    void SetTacticState(ETacticState NewState);

    UFUNCTION(BlueprintCallable, Category = "AI")
    ETacticState GetTacticState() const { return CurrentTactic; }

    UFUNCTION(BlueprintCallable, Category = "AI")
    void SetSquadRole(ESquadRole NewRole);

    UFUNCTION(BlueprintCallable, Category = "AI")
    ESquadRole GetSquadRole() const { return AssignedRole; }

    UFUNCTION(BlueprintCallable, Category = "Squad")
    void RegisterWithSquad(ASquadManager* Manager);

    UFUNCTION(BlueprintCallable, Category = "Stats")
    void AddKill();

    UFUNCTION(BlueprintCallable, Category = "Stats")
    void AddDeath();

    UFUNCTION(BlueprintCallable, Category = "Stats")
    void AddRevive();

    UFUNCTION(BlueprintCallable, Category = "Stats")
    void AddFlankSuccess();

    UFUNCTION(BlueprintCallable, Category = "Stats")
    FSquadMateStats GetStats() const { return AgentStats; }

    // AI Perception Callbacks
    UFUNCTION()
    void OnPerceptionUpdated(const TArray<AActor*>& UpdatedActors);

    UFUNCTION()
    void OnTargetPerceptionUpdated(AActor* Actor, FAIStimulus Stimulus);

protected:
    // Internal Methods
    void InitializeAIPerception();
    void InitializeBehaviorTree();
    void UpdateBlackboardValues();
    void ProcessEnemyDetection(AActor* Enemy);
    void ProcessAllyDetection(AActor* Ally);
    void EvaluateTacticalSituation();

    // Combat Logic
    bool CanEngageTarget(AActor* Target) const;
    bool ShouldRetreat() const;
    bool ShouldSeekCover() const;
    bool ShouldFlank() const;
    bool ShouldReviveAlly() const;

    // Utility Functions
    float GetDistanceToTarget(AActor* Target) const;
    bool IsInLineOfSight(AActor* Target) const;
    bool HasClearShotToTarget(AActor* Target) const;

private:
    // Internal State
    float LastCombatTime;
    float LastDecisionTime;
    TArray<AActor*> DetectedEnemies;
    TArray<AActor*> DetectedAllies;
    
    // Decision Making
    void MakeDecision();
    ETacticState DetermineBestTactic();
    void ExecuteTactic(ETacticState Tactic);
};
