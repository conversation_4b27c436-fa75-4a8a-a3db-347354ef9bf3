{"version": "3.0", "description": "SquadMate AI Decision Tree - Production-Ready Configuration with Enhanced Tactical Logic", "lastModified": "2024-12-19T12:00:00Z", "gameMode": "5v5_<PERSON><PERSON><PERSON><PERSON><PERSON>", "agent_roles": ["Assault", "Support", "Scout", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "tactics": {"Engage": {"conditions": {"HasLineOfSight": true, "TargetActor": "valid", "HealthAbove": 50, "AmmoCount": ">5"}, "actions": ["FireWeapon", "PeekFire", "SuppressFire"], "weight": 0.8, "priority": 4}, "Flank": {"conditions": {"HasLineOfSight": false, "TargetActor": "valid", "SquadLeaderNearby": true, "SquadRole": ["Scout", "Assault"]}, "actions": ["EQS_FindFlankPosition", "MoveTo", "FireWeapon"], "weight": 0.5, "priority": 5}, "Revive": {"conditions": {"ReviveTarget": "valid", "HealthAbove": 60, "IsUnderFire": false}, "actions": ["MoveTo", "ReviveAlly"], "weight": 0.9, "priority": 2}, "Retreat": {"conditions": {"IsUnderFire": true, "HealthBelow": 30, "AmmoCount": "<10"}, "actions": ["FindCover", "HoldPosition", "Heal"], "weight": 0.6, "priority": 3}, "Patrol": {"conditions": {"TargetActor": "null", "IsUnderFire": false}, "actions": ["PatrolZone", "ScanForThreats"], "weight": 0.3, "priority": 9}}, "fallback": {"actions": ["Patrol", "HoldPosition"], "weight": 0.2}, "decisionTree": {"rootNode": {"type": "Selector", "name": "MainDecisionHub", "priority": 1, "children": [{"type": "Sequence", "name": "RevivingState", "priority": 1, "conditions": [{"blackboardKey": "IsReviving", "operator": "equals", "value": true, "weight": 1.0}], "actions": [{"type": "Wait", "name": "WaitWhileReviving", "duration": -1, "interruptible": true}]}, {"type": "Sequence", "name": "ReviveAlly", "priority": 2, "conditions": [{"blackboardKey": "Revive<PERSON><PERSON><PERSON>", "operator": "notNull", "weight": 1.0}, {"blackboardKey": "IsUnderFire", "operator": "equals", "value": false, "weight": 0.8}], "cooldown": 2.0, "actions": [{"type": "MoveTo", "target": "Revive<PERSON><PERSON><PERSON>", "acceptanceRadius": 150.0, "usePathfinding": true}, {"type": "CustomTask", "taskClass": "BTTask_ReviveAlly", "parameters": {"reviveTime": 3.0, "safetyRadius": 400.0, "requireCover": false}}]}, {"type": "Sequence", "name": "UnderFireEmergency", "priority": 3, "conditions": [{"blackboardKey": "IsUnderFire", "operator": "equals", "value": true, "weight": 1.0}], "cooldown": 0.5, "actions": [{"type": "<PERSON><PERSON><PERSON>", "name": "EmergencyResponse", "children": [{"type": "CustomTask", "taskClass": "BTTask_FindCover", "parameters": {"searchRadius": 600.0, "emergencyMode": true, "minCoverHeight": 100.0}}, {"type": "CustomTask", "taskClass": "BTTask_SuppressiveFire", "parameters": {"duration": 2.0, "suppressionMode": "Retreat", "ammoLimit": 10}}]}, {"type": "MoveTo", "target": "CoverLocation", "acceptanceRadius": 100.0, "movementSpeed": 800.0}]}, {"type": "Sequence", "name": "DirectEngagement", "priority": 4, "conditions": [{"blackboardKey": "TargetActor", "operator": "notNull", "weight": 1.0}, {"blackboardKey": "HasLineOfSight", "operator": "equals", "value": true, "weight": 0.9}, {"blackboardKey": "AmmoCount", "operator": "greaterThan", "value": 5, "weight": 0.8}], "services": [{"type": "BTService_UpdateCombatState", "interval": 0.1}], "actions": [{"type": "Selector", "name": "CombatStanceSelection", "children": [{"type": "Sequence", "name": "CloseRangeCombat", "conditions": [{"blackboardKey": "DistanceToTarget", "operator": "lessThan", "value": 300.0, "weight": 1.0}], "actions": [{"type": "CustomTask", "taskClass": "BTTask_SetStance", "parameters": {"stance": "Standing", "allowCrouching": true}}, {"type": "CustomTask", "taskClass": "BTTask_FireWeapon", "parameters": {"fireMode": "Aggressive", "burstSize": 5, "fireRate": 0.08}}]}, {"type": "Sequence", "name": "MediumRangeCombat", "conditions": [{"blackboardKey": "DistanceToTarget", "operator": "between", "minValue": 300.0, "maxValue": 800.0, "weight": 1.0}], "actions": [{"type": "CustomTask", "taskClass": "BTTask_SetStance", "parameters": {"stance": "Crouching"}}, {"type": "CustomTask", "taskClass": "BTTask_FireWeapon", "parameters": {"fireMode": "BurstFire", "burstSize": 3, "fireRate": 0.1}}]}, {"type": "Sequence", "name": "LongRangeCombat", "conditions": [{"blackboardKey": "DistanceToTarget", "operator": "greaterThan", "value": 800.0, "weight": 1.0}], "actions": [{"type": "CustomTask", "taskClass": "BTTask_SetStance", "parameters": {"stance": "Prone", "allowCrouching": true}}, {"type": "CustomTask", "taskClass": "BTTask_FireWeapon", "parameters": {"fireMode": "Precision", "burstSize": 1, "fireRate": 0.5}}]}]}]}, {"type": "Sequence", "name": "FlankingManeuver", "priority": 5, "conditions": [{"blackboardKey": "TargetActor", "operator": "notNull", "weight": 1.0}, {"blackboardKey": "TacticState", "operator": "equals", "value": "Flank", "weight": 1.0}, {"blackboardKey": "SquadRole", "operator": "in", "values": ["Scout", "Assault"], "weight": 0.8}], "actions": [{"type": "CustomTask", "taskClass": "BTTask_EQS_FindFlankPosition", "parameters": {"flankType": "Optimal", "minFlankDistance": 400.0, "maxFlankDistance": 1000.0, "stealthPriority": 0.7}}, {"type": "MoveTo", "target": "FlankLocation", "acceptanceRadius": 100.0, "movementSpeed": 500.0, "useStealthMovement": true}, {"type": "CustomTask", "taskClass": "BTTask_SetStance", "parameters": {"stance": "Crouching"}}, {"type": "CustomTask", "taskClass": "BTTask_FireWeapon", "parameters": {"fireMode": "Flanking", "waitForOptimalShot": true}}]}, {"type": "Sequence", "name": "TacticalPositioning", "priority": 6, "conditions": [{"blackboardKey": "TargetActor", "operator": "notNull", "weight": 1.0}, {"blackboardKey": "HasLineOfSight", "operator": "equals", "value": false, "weight": 1.0}], "actions": [{"type": "CustomTask", "taskClass": "BTTask_FindCover", "parameters": {"coverType": "Tactical", "preferOffensiveCover": true, "searchRadius": 800.0}}, {"type": "MoveTo", "target": "CoverLocation", "acceptanceRadius": 100.0}, {"type": "Selector", "name": "CoverBeh<PERSON>or", "children": [{"type": "Sequence", "name": "PeekAndShoot", "conditions": [{"blackboardKey": "HasLineOfSight", "operator": "equals", "value": true, "weight": 1.0}], "actions": [{"type": "CustomTask", "taskClass": "BTTask_PeekFromCover", "parameters": {"peekDuration": 2.0, "peekDirection": "Auto"}}, {"type": "CustomTask", "taskClass": "BTTask_FireWeapon", "parameters": {"fireMode": "Controlled"}}, {"type": "CustomTask", "taskClass": "BTTask_ReturnToCover"}]}, {"type": "CustomTask", "taskClass": "BTTask_WaitInCover", "parameters": {"waitDuration": 3.0, "scanForTargets": true}}]}]}, {"type": "Sequence", "name": "ReloadAndReposition", "priority": 7, "conditions": [{"blackboardKey": "AmmoCount", "operator": "lessThan", "value": 10, "weight": 1.0}], "actions": [{"type": "CustomTask", "taskClass": "BTTask_FindCover", "parameters": {"coverType": "Safe", "searchRadius": 500.0, "prioritizeSafety": true}}, {"type": "MoveTo", "target": "CoverLocation", "acceptanceRadius": 100.0}, {"type": "CustomTask", "taskClass": "BTTask_Reload", "parameters": {"reloadType": "Full", "coverWhileReloading": true}}, {"type": "CustomTask", "taskClass": "BTTask_CallForCover", "parameters": {"message": "Reloading, need cover!"}}]}, {"type": "Sequence", "name": "SquadCoordination", "priority": 8, "conditions": [{"blackboardKey": "SquadLeader", "operator": "notNull", "weight": 1.0}], "services": [{"type": "BTService_SquadCommunication", "interval": 0.5}], "actions": [{"type": "Selector", "name": "RoleBasedBehavior", "children": [{"type": "Sequence", "name": "SupportRole", "conditions": [{"blackboardKey": "SquadRole", "operator": "equals", "value": "Support", "weight": 1.0}], "actions": [{"type": "CustomTask", "taskClass": "BTTask_SupportBehavior", "parameters": {"healPriority": 0.9, "revivePriority": 0.9, "combatPriority": 0.4}}]}, {"type": "Sequence", "name": "AssaultRole", "conditions": [{"blackboardKey": "SquadRole", "operator": "equals", "value": "Assault", "weight": 1.0}], "actions": [{"type": "CustomTask", "taskClass": "BTTask_AssaultBehavior", "parameters": {"aggressionLevel": 0.8, "advancePriority": 0.7}}]}, {"type": "Sequence", "name": "ScoutRole", "conditions": [{"blackboardKey": "SquadRole", "operator": "equals", "value": "Scout", "weight": 1.0}], "actions": [{"type": "CustomTask", "taskClass": "BTTask_ScoutBehavior", "parameters": {"flankPriority": 0.9, "intelGathering": 0.8}}]}]}, {"type": "CustomTask", "taskClass": "BTTask_MaintainFormation", "parameters": {"formationType": "Dynamic", "maxDistance": 300.0}}]}, {"type": "Sequence", "name": "PatrolAndHold", "priority": 9, "services": [{"type": "BTService_AreaScan", "interval": 1.0}], "actions": [{"type": "Selector", "name": "MovementDecision", "children": [{"type": "Sequence", "name": "FollowSquadLeader", "conditions": [{"blackboardKey": "SquadLeader", "operator": "notNull", "weight": 1.0}, {"blackboardKey": "DistanceToLeader", "operator": "greaterThan", "value": 500.0, "weight": 0.8}], "actions": [{"type": "MoveTo", "target": "SquadLeader", "acceptanceRadius": 300.0, "maintainDistance": true}]}, {"type": "Sequence", "name": "PatrolAssignedZone", "conditions": [{"blackboardKey": "PreferredZone", "operator": "notNull", "weight": 1.0}], "actions": [{"type": "CustomTask", "taskClass": "BTTask_PatrolZone", "parameters": {"patrolRadius": 400.0, "patrolSpeed": 300.0, "alertnessLevel": 0.7}}]}, {"type": "CustomTask", "taskClass": "BTTask_HoldPosition", "parameters": {"holdRadius": 200.0, "scanInterval": 2.0}}]}, {"type": "CustomTask", "taskClass": "BTTask_ScanForThreats", "parameters": {"scanRadius": 1000.0, "scanFrequency": 0.5}}]}]}}, "runtimeParameters": {"decisionFrequency": 0.1, "maxDecisionTime": 2.0, "enableDynamicWeights": true, "adaptToPlayerBehavior": true, "logDecisions": true}, "loadingInstructions": {"parseOrder": ["conditions", "actions", "services", "children"], "validationRules": {"requireValidBlackboardKeys": true, "validateTaskClasses": true, "checkParameterTypes": true}, "errorHandling": {"onInvalidNode": "skip", "onMissingTask": "useDefault", "onParameterError": "useDefault"}}}