# 🎮 TDM AI Blueprint Setup Guide

## 📋 Overview

This guide provides detailed instructions for setting up Blueprints in the Unreal Engine Content Browser to integrate the TDM AI system with Victor 3D models and TDM map assets.

## 📁 Content Browser Structure

### Recommended Folder Organization:
```
Content/
├── AI/
│   ├── BehaviorTrees/
│   │   ├── BT_TDM_Enhanced.uasset
│   │   └── BT_TDM_Tactical.uasset
│   ├── Blackboards/
│   │   ├── BB_TDM_Enhanced.uasset
│   │   └── BB_TDM_Tactical.uasset
│   ├── Tasks/
│   │   ├── BTTask_TDM_Engage.uasset
│   │   ├── BTTask_TDM_Flank.uasset
│   │   ├── BTTask_TDM_Revive.uasset
│   │   └── BTTask_TDM_Suppress.uasset
│   └── Configurations/
│       └── TDM_AI_Config.json
├── Characters/
│   └── Victor/
│       ├── SK_Victor.uasset
│       ├── ABP_Victor.uasset
│       ├── BP_VictorCharacter.uasset
│       └── Materials/
│           ├── M_Victor_mat0.uasset
│           ├── M_Victor_mat0_001.uasset
│           └── M_Victor_mat0_002.uasset
├── GameModes/
│   ├── BP_TDMGameMode.uasset
│   ├── BP_TDMPlayerController.uasset
│   └── BP_TDMGameState.uasset
├── Maps/
│   └── TDM/
│       ├── TDM_Warehouse.umap
│       ├── SM_TDM_Map.uasset
│       └── Materials/
├── Weapons/
│   ├── BP_WeaponSystem.uasset
│   ├── Meshes/
│   └── Materials/
└── UI/
    ├── WBP_TDM_HUD.uasset
    ├── WBP_Scoreboard.uasset
    └── WBP_MatchStats.uasset
```

## 🎯 Step-by-Step Blueprint Creation

### 1. Victor Character Blueprint

#### Create BP_VictorCharacter:
1. **Right-click in Content/Characters/Victor/**
2. **Create Blueprint Class**
3. **Parent Class**: Select `VictorCharacter` (C++ class)
4. **Name**: `BP_VictorCharacter`

#### Configure BP_VictorCharacter:
```cpp
// Components Tab:
- Mesh (SkeletalMeshComponent): Set to SK_Victor
- WeaponMesh (StaticMeshComponent): Attach to weapon socket
- EquipmentMesh (StaticMeshComponent): Attach to equipment socket
- HealthComponent: Configure max health = 100
- WeaponSystem: Set infinite ammo = true
- ReviveComponent: Set revive time = 5.0
```

#### Event Graph Setup:
```blueprint
Event BeginPlay
├── Call Parent: BeginPlay
├── Set Team ID (from AI Controller)
├── Initialize Weapon System
└── Setup Team Visuals

Event Tick
├── Update Animation State
├── Handle Slide Movement
├── Update Aim Rotation
└── Process Combat State

Custom Events:
- OnTakeDamage
- OnDeath
- OnRevived
- OnRespawn
```

### 2. TDM Game Mode Blueprint

#### Create BP_TDMGameMode:
1. **Right-click in Content/GameModes/**
2. **Create Blueprint Class**
3. **Parent Class**: Select `TDMGameMode` (C++ class)
4. **Name**: `BP_TDMGameMode`

#### Configure BP_TDMGameMode:
```cpp
// Class Defaults:
- Default Pawn Class: BP_VictorCharacter
- Player Controller Class: TDMAIController
- Game State Class: BP_TDMGameState (if created)

// Match Configuration:
- Kill Limit: 40
- Time Limit: 600.0 (10 minutes)
- Respawn Delay: 3.0
- Spawn Protection Time: 3.0
- Infinite Ammo: true
- Auto Pickup: true
```

#### Event Graph Setup:
```blueprint
Event BeginPlay
├── Call Parent: BeginPlay
├── Initialize Teams
├── Setup Spawn System
├── Spawn AI Players
└── Start Match Timer

Custom Events:
- OnPlayerKilled
- OnMatchStateChanged
- OnTeamScoreChanged
- OnMatchFinished
```

### 3. Victor Animation Blueprint

#### Create ABP_Victor:
1. **Right-click in Content/Characters/Victor/**
2. **Create Animation Blueprint**
3. **Target Skeleton**: Victor skeleton from SK_Victor
4. **Name**: `ABP_Victor`

#### Animation Graph Setup:
```blueprint
// State Machine: Locomotion
States:
- Idle
- Walking
- Running
- Crouching
- Prone
- Combat
- Aiming
- Firing
- Reloading
- Reviving
- Down
- Dead

// Blend Spaces:
- BS_Locomotion (Speed, Direction)
- BS_Combat (Aim Pitch, Aim Yaw)
- BS_Crouch (Speed, Direction)

// Animation Sequences:
- Idle_Rifle
- Walk_Rifle
- Run_Rifle
- Crouch_Idle
- Prone_Idle
- Fire_Rifle
- Reload_Rifle
- Revive_Start
- Revive_Loop
- Death_Rifle
```

#### Event Graph Variables:
```cpp
// Movement Variables:
- Speed (Float)
- Direction (Float)
- IsMoving (Bool)
- IsCrouching (Bool)
- IsProne (Bool)

// Combat Variables:
- IsAiming (Bool)
- IsFiring (Bool)
- IsReloading (Bool)
- AimPitch (Float)
- AimYaw (Float)

// State Variables:
- IsReviving (Bool)
- IsBeingRevived (Bool)
- IsDead (Bool)
- IsDown (Bool)
- TacticState (Enum)
```

### 4. Behavior Tree Setup

#### Create BT_TDM_Enhanced:
1. **Right-click in Content/AI/BehaviorTrees/**
2. **Create Behavior Tree**
3. **Name**: `BT_TDM_Enhanced`

#### Behavior Tree Structure:
```
Root
└── Selector: Main Decision
    ├── Sequence: Emergency Response
    │   ├── Decorator: Blackboard (IsUnderFire)
    │   ├── Task: Find Emergency Cover
    │   └── Task: Return Fire
    ├── Sequence: Revive Priority
    │   ├── Decorator: Blackboard (ReviveTarget != null)
    │   ├── Task: Move To Revive Target
    │   └── Task: Revive Ally
    ├── Sequence: Combat Engagement
    │   ├── Decorator: Blackboard (TargetActor != null)
    │   ├── Selector: Combat Tactics
    │   │   ├── Sequence: Direct Engagement
    │   │   ├── Sequence: Flanking
    │   │   └── Sequence: Suppressive Fire
    │   └── Task: Engage Target
    └── Sequence: Patrol/Positioning
        ├── Task: Move To Strategic Position
        └── Task: Watch Angles
```

#### Create BB_TDM_Enhanced:
1. **Right-click in Content/AI/Blackboards/**
2. **Create Blackboard**
3. **Name**: `BB_TDM_Enhanced`

#### Blackboard Keys:
```cpp
// Target Information:
- TargetActor (Object - Actor)
- HasLineOfSight (Bool)
- TargetDistance (Float)

// Combat State:
- IsInCombat (Bool)
- IsUnderFire (Bool)
- TacticState (Enum)
- CurrentWeapon (Enum)

// Team Information:
- TeamMates (Object Array)
- Enemies (Object Array)
- ReviveTarget (Object - Actor)
- IsReviving (Bool)

// Positioning:
- CoverLocation (Vector)
- FlankPosition (Vector)
- CurrentLane (Enum)
- PatrolPoint (Vector)

// Status:
- HealthLow (Bool)
- AmmoLow (Bool)
- SpawnProtected (Bool)
```

### 5. Custom AI Tasks

#### Create BTTask_TDM_Engage:
1. **Right-click in Content/AI/Tasks/**
2. **Create Blueprint Class**
3. **Parent Class**: `BTTask_BlueprintBase`
4. **Name**: `BTTask_TDM_Engage`

#### Task Implementation:
```blueprint
Event Receive Execute AI
├── Get AI Controller (cast to TDMAIController)
├── Get Current Target from Blackboard
├── Check Line of Sight
├── Start Aiming
├── Start Firing
├── Set Tactic State to Engage
└── Finish Execute (Success)

Event Receive Abort AI
├── Stop Firing
├── Stop Aiming
└── Finish Abort
```

#### Create BTTask_TDM_Flank:
```blueprint
Event Receive Execute AI
├── Get AI Controller
├── Find Flank Position (EQS Query)
├── Set Flank Position in Blackboard
├── Move To Flank Position
├── Set Tactic State to Flank
└── Finish Execute (Success)
```

#### Create BTTask_TDM_Revive:
```blueprint
Event Receive Execute AI
├── Get Revive Target from Blackboard
├── Check Distance to Target
├── Move To Revive Target
├── Start Revive Animation
├── Set Is Reviving = true
├── Wait for Revive Complete
└── Finish Execute (Success)
```

### 6. UI Blueprints

#### Create WBP_TDM_HUD:
1. **Right-click in Content/UI/**
2. **Create Widget Blueprint**
3. **Name**: `WBP_TDM_HUD`

#### HUD Elements:
```cpp
// Components:
- Team Score Display (Text)
- Kill Count Display (Text)
- Time Remaining (Text)
- Minimap (Image)
- Health Bar (Progress Bar)
- Ammo Counter (Text)
- Crosshair (Image)
```

#### Create WBP_Scoreboard:
```cpp
// Components:
- Team 1 Player List (Vertical Box)
- Team 2 Player List (Vertical Box)
- Player Stats (K/D/A columns)
- Match Timer (Text)
- Team Scores (Text)
```

### 7. Material Setup for Victor

#### Create Victor Materials:
1. **Import textures from victor/textures/ folder**
2. **Create materials in Content/Characters/Victor/Materials/**

#### M_Victor_mat0:
```cpp
// Material Properties:
- Base Color: Connect mat0_baseColor texture
- Roughness: 0.8
- Metallic: 0.1
- Normal: (if normal map available)
```

#### M_Victor_mat0_001 and M_Victor_mat0_002:
```cpp
// Similar setup with respective textures
- Base Color: Connect respective baseColor textures
- Configure material properties for clothing/equipment
```

### 8. Map Setup

#### Import TDM Map:
1. **Import tdm/source/Tdm.fbx to Content/Maps/TDM/**
2. **Create level: TDM_Warehouse.umap**
3. **Place imported mesh as level geometry**

#### Configure Spawn Points:
```cpp
// Team 0 Spawn Points (Blue):
- Place 5 PlayerStart actors
- Set Player Start Tag = "Team0"
- Position behind blue spawn area

// Team 1 Spawn Points (Red):
- Place 5 PlayerStart actors
- Set Player Start Tag = "Team1"
- Position behind red spawn area
```

#### Lighting Setup:
```cpp
// Basic Lighting:
- Directional Light (sun)
- Sky Light (ambient)
- Point Lights for indoor areas
- Light Mass Importance Volume
```

## 🔧 Blueprint Compilation and Testing

### Compilation Order:
1. **Compile C++ classes first**
2. **Compile Blackboard (BB_TDM_Enhanced)**
3. **Compile Behavior Tree (BT_TDM_Enhanced)**
4. **Compile AI Tasks**
5. **Compile Character Blueprint (BP_VictorCharacter)**
6. **Compile Game Mode (BP_TDMGameMode)**
7. **Compile Animation Blueprint (ABP_Victor)**

### Testing Checklist:
- [ ] Victor character spawns with correct mesh and materials
- [ ] AI controller possesses character correctly
- [ ] Behavior tree executes without errors
- [ ] Weapon system initializes properly
- [ ] Team assignment works correctly
- [ ] Match flow functions as expected

## 🎮 Final Integration

### Level Setup:
1. **Open TDM_Warehouse.umap**
2. **Set World Settings > Game Mode = BP_TDMGameMode**
3. **Place spawn points for both teams**
4. **Configure lighting and post-process**

### Play Testing:
1. **Click Play in Editor**
2. **Observe 5v5 AI match initialization**
3. **Monitor AI behavior and combat**
4. **Verify scoring and respawn systems**

This Blueprint setup provides a complete integration of the TDM AI system with the Victor character model and TDM map assets, ready for production use.
