#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameModeBase.h"
#include "AI/TDMAIController.h"
#include "TDMGameMode.generated.h"

class ATDMMatchManager;
class ATDMSpawnSystem;
class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>;

// TDM Match State
UENUM(BlueprintType)
enum class ETDMMatchState : uint8
{
    WaitingToStart  UMETA(DisplayName = "Waiting To Start"),
    InProgress      UMETA(DisplayName = "In Progress"),
    Finished        UMETA(DisplayName = "Finished"),
    Overtime        UMETA(DisplayName = "Overtime")
};

// Team Information
USTRUCT(BlueprintType)
struct FTDMTeamInfo
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 TeamID;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString TeamName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FLinearColor TeamColor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 KillCount;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<class APawn*> TeamMembers;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FVector> SpawnLocations;

    FTDMTeamInfo()
    {
        TeamID = 0;
        TeamName = TEXT("Team");
        TeamColor = FLinearColor::White;
        KillCount = 0;
    }
};

// Player Statistics
USTRUCT(BlueprintType)
struct FTDMPlayerStats
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString PlayerName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 Kills;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 Deaths;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 Assists;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float Accuracy;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float DamageDealt;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 TeamID;

    FTDMPlayerStats()
    {
        Kills = 0;
        Deaths = 0;
        Assists = 0;
        Accuracy = 0.0f;
        DamageDealt = 0.0f;
        TeamID = 0;
    }
};

// Match Configuration
USTRUCT(BlueprintType)
struct FTDMMatchConfig
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 KillLimit;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float TimeLimit; // In seconds

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float RespawnDelay;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float SpawnProtectionTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bInfiniteAmmo;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bAutoPickup;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<ETDMWeaponType> AllowedWeapons;

    FTDMMatchConfig()
    {
        KillLimit = 40;
        TimeLimit = 600.0f; // 10 minutes
        RespawnDelay = 3.0f;
        SpawnProtectionTime = 3.0f;
        bInfiniteAmmo = true;
        bAutoPickup = true;
    }
};

// Events
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnMatchStateChanged, ETDMMatchState, NewState);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPlayerKilled, APawn*, Victim, APawn*, Killer);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnTeamScoreChanged, int32, TeamID);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnMatchFinished, int32, WinningTeamID);

/**
 * TDM Game Mode - Manages 5v5 Team Deathmatch matches with PUBG Mobile mechanics
 */
UCLASS(BlueprintType, Blueprintable)
class SQUADMATEAI_API ATDMGameMode : public AGameModeBase
{
    GENERATED_BODY()

public:
    ATDMGameMode();

protected:
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

public:
    // Core Components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "TDM Components")
    ATDMMatchManager* MatchManager;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "TDM Components")
    ATDMSpawnSystem* SpawnSystem;

    // Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "TDM Configuration")
    FTDMMatchConfig MatchConfig;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "TDM Configuration")
    TSubclassOf<class AVictorCharacter> VictorCharacterClass;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "TDM Configuration")
    TSubclassOf<ATDMAIController> TDMAIControllerClass;

    // Match State
    UPROPERTY(BlueprintReadOnly, Category = "TDM State")
    ETDMMatchState CurrentMatchState;

    UPROPERTY(BlueprintReadOnly, Category = "TDM State")
    float MatchTimeRemaining;

    UPROPERTY(BlueprintReadOnly, Category = "TDM State")
    TArray<FTDMTeamInfo> Teams;

    UPROPERTY(BlueprintReadOnly, Category = "TDM State")
    TArray<FTDMPlayerStats> PlayerStats;

    // Events
    UPROPERTY(BlueprintAssignable, Category = "TDM Events")
    FOnMatchStateChanged OnMatchStateChanged;

    UPROPERTY(BlueprintAssignable, Category = "TDM Events")
    FOnPlayerKilled OnPlayerKilled;

    UPROPERTY(BlueprintAssignable, Category = "TDM Events")
    FOnTeamScoreChanged OnTeamScoreChanged;

    UPROPERTY(BlueprintAssignable, Category = "TDM Events")
    FOnMatchFinished OnMatchFinished;

    // Public Interface
    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    void StartMatch();

    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    void EndMatch(int32 WinningTeamID = -1);

    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    void PauseMatch();

    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    void ResumeMatch();

    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    void RestartMatch();

    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    void RegisterKill(APawn* Victim, APawn* Killer);

    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    void RegisterDeath(APawn* Victim, APawn* Killer);

    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    void SpawnPlayer(int32 TeamID, ETDMRole Role = ETDMRole::Assault);

    UFUNCTION(BlueprintCallable, Category = "TDM Game Mode")
    void RespawnPlayer(APawn* Player);

    // Getters
    UFUNCTION(BlueprintPure, Category = "TDM Game Mode")
    int32 GetTeamScore(int32 TeamID) const;

    UFUNCTION(BlueprintPure, Category = "TDM Game Mode")
    int32 GetWinningTeamID() const;

    UFUNCTION(BlueprintPure, Category = "TDM Game Mode")
    bool IsMatchInProgress() const { return CurrentMatchState == ETDMMatchState::InProgress; }

    UFUNCTION(BlueprintPure, Category = "TDM Game Mode")
    float GetMatchProgress() const;

    UFUNCTION(BlueprintPure, Category = "TDM Game Mode")
    TArray<FTDMPlayerStats> GetPlayerStatsByTeam(int32 TeamID) const;

    UFUNCTION(BlueprintPure, Category = "TDM Game Mode")
    FTDMPlayerStats GetPlayerStats(APawn* Player) const;

protected:
    // Internal State
    float MatchStartTime;
    bool bMatchPaused;
    FTimerHandle MatchTimerHandle;
    FTimerHandle StatsUpdateHandle;

    // Internal Methods
    void InitializeTeams();
    void InitializeSpawnSystem();
    void SetupMatchTimer();
    void UpdateMatchTimer();
    void UpdatePlayerStats();
    void CheckWinConditions();
    void SetMatchState(ETDMMatchState NewState);

    // Team Management
    void CreateTeam(int32 TeamID, const FString& TeamName, const FLinearColor& TeamColor);
    void AssignPlayerToTeam(APawn* Player, int32 TeamID);
    void BalanceTeams();

    // Spawn Management
    FVector GetSpawnLocation(int32 TeamID) const;
    FRotator GetSpawnRotation(int32 TeamID) const;
    bool IsSpawnLocationSafe(const FVector& Location, int32 TeamID) const;

    // Statistics
    void InitializePlayerStats(APawn* Player);
    void UpdatePlayerKillStats(APawn* Player);
    void UpdatePlayerDeathStats(APawn* Player);
    void CalculatePlayerAccuracy(APawn* Player);

    // AI Management
    void SpawnAIPlayers();
    void ConfigureAIPlayer(APawn* AIPlayer, int32 TeamID, ETDMRole Role);
    void UpdateAICoordination();

    // Match Logic
    void HandleMatchStart();
    void HandleMatchEnd(int32 WinningTeamID);
    void HandleTimeLimit();
    void HandleKillLimit(int32 TeamID);

    // Utility
    int32 GetPlayerTeamID(APawn* Player) const;
    FTDMTeamInfo* GetTeamInfo(int32 TeamID);
    FTDMPlayerStats* GetPlayerStatsPtr(APawn* Player);
};
