# ⚙️ Tactical Parameters DataTable Setup Guide

## 📋 Create DT_TacticalParameters DataTable

### Step 1: Create the DataTable
1. **Right-click in Content/Data/DataTables/**
2. **Miscellaneous → Data Table**
3. **Choose Row Structure: `DataTableRowBase`**
4. **Name: `DT_TacticalParameters`**

### Step 2: Add Columns
Add these columns to your DataTable:

| Column Name | Type | Description |
|-------------|------|-------------|
| ParameterName | String | Unique parameter identifier |
| Category | String | Parameter category |
| ValueType | String | Data type (Float, Integer, Boolean, String) |
| FloatValue | Float | Numeric value |
| IntValue | Integer | Integer value |
| BoolValue | Boolean | Boolean value |
| StringValue | String | Text value |
| Description | String | Parameter description |
| MinValue | Float | Minimum allowed value |
| MaxValue | Float | Maximum allowed value |
| DefaultValue | Float | Default/recommended value |

### Step 3: Populate with PUBGM TDM Parameters

#### Engagement Ranges
**Row: EngagementRange_Close**
- ParameterName: "engagement_range_close"
- Category: "Combat"
- ValueType: "Float"
- FloatValue: 100.0
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Close quarters engagement range in units"
- MinValue: 50.0
- MaxValue: 150.0
- DefaultValue: 100.0

**Row: EngagementRange_Medium**
- ParameterName: "engagement_range_medium"
- Category: "Combat"
- ValueType: "Float"
- FloatValue: 300.0
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Medium range engagement distance"
- MinValue: 200.0
- MaxValue: 400.0
- DefaultValue: 300.0

**Row: EngagementRange_Long**
- ParameterName: "engagement_range_long"
- Category: "Combat"
- ValueType: "Float"
- FloatValue: 600.0
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Long range engagement distance"
- MinValue: 500.0
- MaxValue: 800.0
- DefaultValue: 600.0

**Row: EngagementRange_Sniper**
- ParameterName: "engagement_range_sniper"
- Category: "Combat"
- ValueType: "Float"
- FloatValue: 1000.0
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Sniper effective range"
- MinValue: 800.0
- MaxValue: 1200.0
- DefaultValue: 1000.0

#### Reaction Times
**Row: ReactionTime_ImmediateThreat**
- ParameterName: "reaction_time_immediate_threat"
- Category: "AI_Performance"
- ValueType: "Float"
- FloatValue: 0.1
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Reaction time for immediate threats"
- MinValue: 0.05
- MaxValue: 0.2
- DefaultValue: 0.1

**Row: ReactionTime_EnemySpotted**
- ParameterName: "reaction_time_enemy_spotted"
- Category: "AI_Performance"
- ValueType: "Float"
- FloatValue: 0.3
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Reaction time for enemy detection"
- MinValue: 0.2
- MaxValue: 0.5
- DefaultValue: 0.3

**Row: ReactionTime_TacticalDecision**
- ParameterName: "reaction_time_tactical_decision"
- Category: "AI_Performance"
- ValueType: "Float"
- FloatValue: 0.5
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Time for tactical decision making"
- MinValue: 0.3
- MaxValue: 1.0
- DefaultValue: 0.5

**Row: ReactionTime_Positioning**
- ParameterName: "reaction_time_positioning"
- Category: "AI_Performance"
- ValueType: "Float"
- FloatValue: 1.0
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Time for positioning decisions"
- MinValue: 0.5
- MaxValue: 2.0
- DefaultValue: 1.0

#### Team Coordination
**Row: CommunicationRange**
- ParameterName: "communication_range"
- Category: "Team_Coordination"
- ValueType: "Float"
- FloatValue: 500.0
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Range for team communication"
- MinValue: 300.0
- MaxValue: 1000.0
- DefaultValue: 500.0

**Row: SupportDistance**
- ParameterName: "support_distance"
- Category: "Team_Coordination"
- ValueType: "Float"
- FloatValue: 200.0
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Distance to maintain for team support"
- MinValue: 100.0
- MaxValue: 300.0
- DefaultValue: 200.0

**Row: FlankCoordinationTime**
- ParameterName: "flank_coordination_time"
- Category: "Team_Coordination"
- ValueType: "Float"
- FloatValue: 3.0
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Time to coordinate flanking maneuvers"
- MinValue: 2.0
- MaxValue: 5.0
- DefaultValue: 3.0

**Row: RevivePriorityDistance**
- ParameterName: "revive_priority_distance"
- Category: "Team_Coordination"
- ValueType: "Float"
- FloatValue: 150.0
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Maximum distance for revive priority"
- MinValue: 100.0
- MaxValue: 200.0
- DefaultValue: 150.0

#### Movement Speeds (PUBGM Accurate)
**Row: MovementSpeed_Walk**
- ParameterName: "movement_speed_walk"
- Category: "Movement"
- ValueType: "Float"
- FloatValue: 150.0
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Walking speed in units/second"
- MinValue: 100.0
- MaxValue: 200.0
- DefaultValue: 150.0

**Row: MovementSpeed_Run**
- ParameterName: "movement_speed_run"
- Category: "Movement"
- ValueType: "Float"
- FloatValue: 300.0
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Running speed in units/second"
- MinValue: 250.0
- MaxValue: 350.0
- DefaultValue: 300.0

**Row: MovementSpeed_Crouch**
- ParameterName: "movement_speed_crouch"
- Category: "Movement"
- ValueType: "Float"
- FloatValue: 100.0
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Crouching speed in units/second"
- MinValue: 75.0
- MaxValue: 125.0
- DefaultValue: 100.0

**Row: MovementSpeed_Prone**
- ParameterName: "movement_speed_prone"
- Category: "Movement"
- ValueType: "Float"
- FloatValue: 50.0
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Prone movement speed in units/second"
- MinValue: 25.0
- MaxValue: 75.0
- DefaultValue: 50.0

**Row: MovementSpeed_Slide**
- ParameterName: "movement_speed_slide"
- Category: "Movement"
- ValueType: "Float"
- FloatValue: 400.0
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Sliding speed in units/second"
- MinValue: 350.0
- MaxValue: 450.0
- DefaultValue: 400.0

#### Combat Parameters
**Row: PeekDuration**
- ParameterName: "peek_duration"
- Category: "Combat"
- ValueType: "Float"
- FloatValue: 1.5
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Duration of peek action in seconds"
- MinValue: 1.0
- MaxValue: 2.5
- DefaultValue: 1.5

**Row: SuppressionDuration**
- ParameterName: "suppression_duration"
- Category: "Combat"
- ValueType: "Float"
- FloatValue: 3.0
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Duration of suppressive fire in seconds"
- MinValue: 2.0
- MaxValue: 5.0
- DefaultValue: 3.0

**Row: ReloadCoverSeeking**
- ParameterName: "reload_cover_seeking"
- Category: "Combat"
- ValueType: "Boolean"
- FloatValue: 0.0
- IntValue: 0
- BoolValue: true
- StringValue: ""
- Description: "Seek cover when reloading"
- MinValue: 0.0
- MaxValue: 1.0
- DefaultValue: 1.0

**Row: AutoLean**
- ParameterName: "auto_lean"
- Category: "Combat"
- ValueType: "Boolean"
- FloatValue: 0.0
- IntValue: 0
- BoolValue: true
- StringValue: ""
- Description: "Automatically lean around corners"
- MinValue: 0.0
- MaxValue: 1.0
- DefaultValue: 1.0

**Row: RecoilCompensation**
- ParameterName: "recoil_compensation"
- Category: "Combat"
- ValueType: "Float"
- FloatValue: 0.8
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Recoil compensation factor (0.0-1.0)"
- MinValue: 0.0
- MaxValue: 1.0
- DefaultValue: 0.8

#### Health and Survival
**Row: HealthThreshold_Low**
- ParameterName: "health_threshold_low"
- Category: "Survival"
- ValueType: "Float"
- FloatValue: 0.3
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Health percentage considered low"
- MinValue: 0.2
- MaxValue: 0.5
- DefaultValue: 0.3

**Row: HealthThreshold_Critical**
- ParameterName: "health_threshold_critical"
- Category: "Survival"
- ValueType: "Float"
- FloatValue: 0.15
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Health percentage considered critical"
- MinValue: 0.1
- MaxValue: 0.25
- DefaultValue: 0.15

**Row: ReviveTime**
- ParameterName: "revive_time"
- Category: "Survival"
- ValueType: "Float"
- FloatValue: 5.0
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Time required to revive teammate"
- MinValue: 3.0
- MaxValue: 8.0
- DefaultValue: 5.0

**Row: SpawnProtectionTime**
- ParameterName: "spawn_protection_time"
- Category: "Survival"
- ValueType: "Float"
- FloatValue: 3.0
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Spawn protection duration in seconds"
- MinValue: 2.0
- MaxValue: 5.0
- DefaultValue: 3.0

#### Match Parameters
**Row: MatchKillLimit**
- ParameterName: "match_kill_limit"
- Category: "Match"
- ValueType: "Integer"
- FloatValue: 0.0
- IntValue: 40
- BoolValue: false
- StringValue: ""
- Description: "Kills required to win match"
- MinValue: 30.0
- MaxValue: 50.0
- DefaultValue: 40.0

**Row: MatchTimeLimit**
- ParameterName: "match_time_limit"
- Category: "Match"
- ValueType: "Float"
- FloatValue: 600.0
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Match time limit in seconds"
- MinValue: 480.0
- MaxValue: 900.0
- DefaultValue: 600.0

**Row: RespawnDelay**
- ParameterName: "respawn_delay"
- Category: "Match"
- ValueType: "Float"
- FloatValue: 3.0
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "Respawn delay in seconds"
- MinValue: 1.0
- MaxValue: 5.0
- DefaultValue: 3.0

#### AI Difficulty Modifiers
**Row: AIDifficulty_Accuracy**
- ParameterName: "ai_difficulty_accuracy"
- Category: "AI_Difficulty"
- ValueType: "Float"
- FloatValue: 0.75
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "AI accuracy modifier (0.0-1.0)"
- MinValue: 0.3
- MaxValue: 1.0
- DefaultValue: 0.75

**Row: AIDifficulty_ReactionTime**
- ParameterName: "ai_difficulty_reaction_time"
- Category: "AI_Difficulty"
- ValueType: "Float"
- FloatValue: 1.0
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "AI reaction time modifier (1.0=normal)"
- MinValue: 0.5
- MaxValue: 2.0
- DefaultValue: 1.0

**Row: AIDifficulty_Aggression**
- ParameterName: "ai_difficulty_aggression"
- Category: "AI_Difficulty"
- ValueType: "Float"
- FloatValue: 0.7
- IntValue: 0
- BoolValue: false
- StringValue: ""
- Description: "AI aggression level (0.0-1.0)"
- MinValue: 0.3
- MaxValue: 1.0
- DefaultValue: 0.7

### Step 4: Usage in AI System

```blueprint
// Get tactical parameter
Get Data Table Row
├── Data Table: DT_TacticalParameters
├── Row Name: "engagement_range_medium"
└── Out Row: (Parameter struct)
    └── Use FloatValue for calculations

// Dynamic parameter adjustment
Set Data Table Row
├── Data Table: DT_TacticalParameters
├── Row Name: "ai_difficulty_accuracy"
├── New FloatValue: [Adjusted Value]
└── Update AI behavior
```

### Step 5: Runtime Parameter Modification

```blueprint
// Difficulty scaling
For Each (AI Controller)
├── Get Parameter: "ai_difficulty_accuracy"
├── Multiply by Difficulty Modifier
├── Apply to Weapon System
└── Update AI Performance
```

This DataTable provides comprehensive tactical parameters for fine-tuning PUBGM TDM AI behavior and match settings.
