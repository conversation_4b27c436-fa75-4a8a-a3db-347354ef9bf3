#pragma once

#include "CoreMinimal.h"
#include "BehaviorTree/BTTaskNode.h"
#include "AI/SquadMateAIController.h"
#include "Components/HealthComponent.h"
#include "Components/ReviveComponent.h"
#include "BTTask_ReviveAlly.generated.h"

UENUM(BlueprintType)
enum class EReviveResult : uint8
{
    Success         UMETA(DisplayName = "Success"),
    Failed          UMETA(DisplayName = "Failed"),
    Interrupted     UMETA(DisplayName = "Interrupted"),
    TargetDead      UMETA(DisplayName = "Target Dead"),
    TooFarAway      UMETA(DisplayName = "Too Far Away"),
    UnderFire       UMETA(DisplayName = "Under Fire"),
    NoTarget        UMETA(DisplayName = "No Target")
};

/**
 * Behavior Tree task for reviving downed allies
 * Handles approach, safety checks, revive execution, and communication
 */
UCLASS(BlueprintType, meta=(DisplayName="Revive Ally"))
class SQUADMATEAI_API UBTTask_ReviveAlly : public UBTTaskNode
{
    GENERATED_BODY()

public:
    UBTTask_ReviveAlly();

protected:
    virtual EBTNodeResult::Type ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;
    virtual EBTNodeResult::Type AbortTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;
    virtual void TickTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaSeconds) override;
    virtual void OnTaskFinished(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EBTNodeResult::Type TaskResult) override;
    virtual FString GetStaticDescription() const override;

    // Blackboard Keys
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector ReviveTargetKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector IsRevivingKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector IsUnderFireKey;

    // Revive Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Revive")
    float MaxReviveDistance = 200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Revive")
    float ReviveTime = 3.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Revive")
    bool bRequireLineOfSight = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Revive")
    bool bCheckForEnemies = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Revive")
    float EnemyCheckRadius = 500.0f;

    // Safety Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Safety")
    bool bAbortIfUnderFire = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Safety")
    bool bUseCoverWhileReviving = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Safety")
    float SafetyCheckInterval = 0.5f;

    // Animation and Effects
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    class UAnimMontage* ReviveMontage;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    class UParticleSystem* ReviveEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    class USoundBase* ReviveSound;

    // Communication
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Communication")
    bool bCallOutReviveStart = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Communication")
    bool bCallOutReviveComplete = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Communication")
    FString ReviveStartCallout = TEXT("Reviving teammate!");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Communication")
    FString ReviveCompleteCallout = TEXT("Teammate revived!");

    // Debug Options
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bDrawDebugInfo = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bLogReviveEvents = true;

public:
    // Static utility functions
    UFUNCTION(BlueprintCallable, Category = "Revive", CallInEditor = true)
    static bool CanReviveTarget(AActor* Reviver, AActor* Target, float MaxDistance = 200.0f);

    UFUNCTION(BlueprintCallable, Category = "Revive", CallInEditor = true)
    static bool IsReviveSafe(AActor* Reviver, AActor* Target, float SafetyRadius = 500.0f);

    UFUNCTION(BlueprintCallable, Category = "Revive", CallInEditor = true)
    static float GetReviveProgress(AActor* Target);

protected:
    // Core revive logic
    bool InitializeRevive(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    void UpdateRevive(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    void CompleteRevive(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EReviveResult Result);

    // Validation and safety
    bool ValidateReviveTarget(AActor* Target, AActor* Reviver);
    bool IsRevivePositionSafe(const FVector& Position, AActor* Reviver);
    bool CheckForNearbyEnemies(const FVector& Position, float Radius);
    bool IsWithinReviveRange(AActor* Reviver, AActor* Target);

    // Revive execution
    void StartReviveAnimation(AActor* Reviver);
    void StopReviveAnimation(AActor* Reviver);
    void PlayReviveEffects(const FVector& Location);
    void UpdateReviveProgress(UBehaviorTreeComponent& OwnerComp, float DeltaTime);

    // Communication and feedback
    void BroadcastReviveStart(UBehaviorTreeComponent& OwnerComp, AActor* Target);
    void BroadcastReviveComplete(UBehaviorTreeComponent& OwnerComp, AActor* Target);
    void BroadcastReviveFailed(UBehaviorTreeComponent& OwnerComp, AActor* Target, EReviveResult Reason);

    // Positioning and movement
    FVector GetOptimalRevivePosition(AActor* Target, AActor* Reviver);
    bool MoveToRevivePosition(UBehaviorTreeComponent& OwnerComp, const FVector& Position);
    void AdjustReviverStance(AActor* Reviver, AActor* Target);

    // Safety monitoring
    void PerformSafetyCheck(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    bool ShouldAbortRevive(UBehaviorTreeComponent& OwnerComp);
    void HandleReviveInterruption(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);

    // Utility functions
    float GetDistanceToTarget(AActor* From, AActor* To);
    bool HasLineOfSightToTarget(AActor* From, AActor* To);
    TArray<AActor*> GetNearbyEnemies(const FVector& Location, float Radius);

    // Debug and logging
    void LogReviveEvent(const FString& Event, UBehaviorTreeComponent& OwnerComp, AActor* Target = nullptr);
    void DrawDebugReviveInfo(UWorld* World, const FVector& ReviverLocation, 
                           const FVector& TargetLocation, EReviveResult Status);

private:
    // Task memory structure
    struct FBTTask_ReviveAllyMemory
    {
        TWeakObjectPtr<AActor> ReviveTarget;
        float ReviveStartTime = 0.0f;
        float LastSafetyCheck = 0.0f;
        float ReviveProgress = 0.0f;
        bool bReviveInProgress = false;
        bool bAnimationPlaying = false;
        bool bEffectsPlaying = false;
        FVector RevivePosition = FVector::ZeroVector;
        EReviveResult LastResult = EReviveResult::Success;
    };

    // Memory management
    void InitializeTaskMemory(uint8* NodeMemory);
    void CleanupTaskMemory(uint8* NodeMemory);
    FBTTask_ReviveAllyMemory* GetTaskMemory(uint8* NodeMemory);

    // Animation callbacks
    UFUNCTION()
    void OnReviveMontageEnded(UAnimMontage* Montage, bool bInterrupted);

    // Component access helpers
    UHealthComponent* GetHealthComponent(AActor* Actor);
    UReviveComponent* GetReviveComponent(AActor* Actor);
    ASquadMateAIController* GetSquadMateController(UBehaviorTreeComponent& OwnerComp);

    // Revive state management
    void SetRevivingState(UBehaviorTreeComponent& OwnerComp, bool bIsReviving);
    void UpdateBlackboardReviveState(UBehaviorTreeComponent& OwnerComp, bool bIsReviving);

    // Performance optimization
    bool ShouldPerformExpensiveChecks(float CurrentTime, float LastCheckTime);
    void CacheReviveData(uint8* NodeMemory, AActor* Target);
};
