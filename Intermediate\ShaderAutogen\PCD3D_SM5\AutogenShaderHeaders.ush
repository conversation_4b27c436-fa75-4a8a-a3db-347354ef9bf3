// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#if FEATURE_LEVEL >= FEATURE_LEVEL_SM5
float SampleDeviceZFromSceneTexturesTempCopy(float2 UV)
{
	return SceneDepthTexture.SampleLevel(SceneDepthTextureSampler, UV, 0).r;
}
#endif

#ifndef GBUFFER_LAYOUT
#define GBUFFER_LAYOUT 0
#endif

#if GBUFFER_LAYOUT == 0

void EncodeGBufferToMRT(inout FPixelShaderOut Out, FGBufferData GBuffer, float QuantizationBias)
{
	float4 MrtFloat1 = 0.0f;
	float4 MrtFloat2 = 0.0f;
	uint4 MrtUint2 = 0;
	float4 MrtFloat3 = 0.0f;
	float4 MrtFloat4 = 0.0f;
	float4 MrtFloat5 = 0.0f;

	float3 WorldNormal_Compressed = EncodeNormalHelper(GBuffer.WorldNormal, 0.0f);

	MrtFloat1.x = WorldNormal_Compressed.x;
	MrtFloat1.y = WorldNormal_Compressed.y;
	MrtFloat1.z = WorldNormal_Compressed.z;
	MrtFloat1.w = GBuffer.PerObjectGBufferData.x;
	MrtFloat2.x = GBuffer.Metallic.x;
	MrtFloat2.y = GBuffer.Specular.x;
	MrtFloat2.z = GBuffer.Roughness.x;
	MrtUint2.w |= ((((GBuffer.ShadingModelID.x) >> 0) & 0x0f) << 0);
	MrtUint2.w |= ((((GBuffer.SelectiveOutputMask.x) >> 0) & 0x0f) << 4);
	MrtFloat3.x = GBuffer.BaseColor.x;
	MrtFloat3.y = GBuffer.BaseColor.y;
	MrtFloat3.z = GBuffer.BaseColor.z;
	MrtFloat3.w = GBuffer.GenericAO.x;
	MrtFloat5.x = GBuffer.PrecomputedShadowFactors.x;
	MrtFloat5.y = GBuffer.PrecomputedShadowFactors.y;
	MrtFloat5.z = GBuffer.PrecomputedShadowFactors.z;
	MrtFloat5.w = GBuffer.PrecomputedShadowFactors.w;
	MrtFloat4.x = GBuffer.CustomData.x;
	MrtFloat4.y = GBuffer.CustomData.y;
	MrtFloat4.z = GBuffer.CustomData.z;
	MrtFloat4.w = GBuffer.CustomData.w;

	Out.MRT[1] = MrtFloat1;
	Out.MRT[2] = float4(MrtFloat2.x, MrtFloat2.y, MrtFloat2.z, float(MrtUint2.w) / 255.0f);
	Out.MRT[3] = MrtFloat3;
	Out.MRT[4] = MrtFloat4;
	Out.MRT[5] = MrtFloat5;
	Out.MRT[6] = float4(0.0f, 0.0f, 0.0f, 0.0f);
	Out.MRT[7] = float4(0.0f, 0.0f, 0.0f, 0.0f);
}


FGBufferData  DecodeGBufferDataDirect(float4 InMRT1,
	float4 InMRT2,
	float4 InMRT3,
	float4 InMRT4,
	float4 InMRT5,
		 
	float CustomNativeDepth,
	float4 AnisotropicData,
	uint CustomStencil,
	float SceneDepth,
	bool bGetNormalizedNormal,
	bool bChecker)
{
	FGBufferData Ret = (FGBufferData)0;
	float3 WorldNormal_Compressed = 0.0f;
	WorldNormal_Compressed.x = InMRT1.x;
	WorldNormal_Compressed.y = InMRT1.y;
	WorldNormal_Compressed.z = InMRT1.z;
	Ret.PerObjectGBufferData.x = InMRT1.w;
	Ret.Metallic.x = InMRT2.x;
	Ret.Specular.x = InMRT2.y;
	Ret.Roughness.x = InMRT2.z;
	Ret.ShadingModelID.x = (((uint((float(InMRT2.w) * 255.0f) + .5f) >> 0) & 0x0f) << 0);
	Ret.SelectiveOutputMask.x = (((uint((float(InMRT2.w) * 255.0f) + .5f) >> 4) & 0x0f) << 0);
	Ret.BaseColor.x = InMRT3.x;
	Ret.BaseColor.y = InMRT3.y;
	Ret.BaseColor.z = InMRT3.z;
	Ret.GenericAO.x = InMRT3.w;
	Ret.PrecomputedShadowFactors.x = InMRT5.x;
	Ret.PrecomputedShadowFactors.y = InMRT5.y;
	Ret.PrecomputedShadowFactors.z = InMRT5.z;
	Ret.PrecomputedShadowFactors.w = InMRT5.w;
	Ret.CustomData.x = InMRT4.x;
	Ret.CustomData.y = InMRT4.y;
	Ret.CustomData.z = InMRT4.z;
	Ret.CustomData.w = InMRT4.w;
	
	Ret.WorldNormal = DecodeNormalHelper(WorldNormal_Compressed);
	Ret.WorldTangent = AnisotropicData.xyz;
	Ret.Anisotropy = AnisotropicData.w;

	GBufferPostDecode(Ret,bChecker,bGetNormalizedNormal);

	Ret.CustomDepth = ConvertFromDeviceZ(CustomNativeDepth);
	Ret.CustomStencil = CustomStencil;
	Ret.Depth = SceneDepth;
	

	return Ret;
}


#if FEATURE_LEVEL >= FEATURE_LEVEL_SM5

// @param PixelPos relative to left top of the rendertarget (not viewport)
FGBufferData DecodeGBufferDataUV(float2 UV, bool bGetNormalizedNormal = true)
{
	float CustomNativeDepth = Texture2DSampleLevel(SceneTexturesStruct.CustomDepthTexture, SceneTexturesStruct_CustomDepthTextureSampler, UV, 0).r;
	int2 IntUV = (int2)trunc(UV * View.BufferSizeAndInvSize.xy * View.BufferToSceneTextureScale.xy);
	uint CustomStencil = SceneTexturesStruct.CustomStencilTexture.Load(int3(IntUV, 0)) STENCIL_COMPONENT_SWIZZLE;
	float SceneDepth = CalcSceneDepth(UV);
	float4 AnisotropicData = Texture2DSampleLevel(SceneTexturesStruct.GBufferFTexture, SceneTexturesStruct_GBufferFTextureSampler, UV, 0).xyzw;

	float4 InMRT1 = Texture2DSampleLevel(SceneTexturesStruct.GBufferATexture, SceneTexturesStruct_GBufferATextureSampler, UV, 0).xyzw;
	float4 InMRT2 = Texture2DSampleLevel(SceneTexturesStruct.GBufferBTexture, SceneTexturesStruct_GBufferBTextureSampler, UV, 0).xyzw;
	float4 InMRT3 = Texture2DSampleLevel(SceneTexturesStruct.GBufferCTexture, SceneTexturesStruct_GBufferCTextureSampler, UV, 0).xyzw;
	float4 InMRT4 = Texture2DSampleLevel(SceneTexturesStruct.GBufferDTexture, SceneTexturesStruct_GBufferDTextureSampler, UV, 0).xyzw;
	float4 InMRT5 = Texture2DSampleLevel(SceneTexturesStruct.GBufferETexture, SceneTexturesStruct_GBufferETextureSampler, UV, 0).xyzw;

	FGBufferData Ret = DecodeGBufferDataDirect(InMRT1,
		InMRT2,
		InMRT3,
		InMRT4,
		InMRT5,
		 
		CustomNativeDepth,
		AnisotropicData,
		CustomStencil,
		SceneDepth,
		bGetNormalizedNormal,
		CheckerFromSceneColorUV(UV));

	return Ret;
}


// @param PixelPos relative to left top of the rendertarget (not viewport)
FGBufferData DecodeGBufferDataUint(uint2 PixelPos, bool bGetNormalizedNormal = true)
{
	float CustomNativeDepth = SceneTexturesStruct.CustomDepthTexture.Load(int3(PixelPos, 0)).r;
	uint CustomStencil = SceneTexturesStruct.CustomStencilTexture.Load(int3(PixelPos, 0)) STENCIL_COMPONENT_SWIZZLE;
	float SceneDepth = CalcSceneDepth(PixelPos);
	float4 AnisotropicData = SceneTexturesStruct.GBufferFTexture.Load(int3(PixelPos, 0)).xyzw;

	float4 InMRT1 = SceneTexturesStruct.GBufferATexture.Load(int3(PixelPos, 0)).xyzw;
	float4 InMRT2 = SceneTexturesStruct.GBufferBTexture.Load(int3(PixelPos, 0)).xyzw;
	float4 InMRT3 = SceneTexturesStruct.GBufferCTexture.Load(int3(PixelPos, 0)).xyzw;
	float4 InMRT4 = SceneTexturesStruct.GBufferDTexture.Load(int3(PixelPos, 0)).xyzw;
	float4 InMRT5 = SceneTexturesStruct.GBufferETexture.Load(int3(PixelPos, 0)).xyzw;

	FGBufferData Ret = DecodeGBufferDataDirect(InMRT1,
		InMRT2,
		InMRT3,
		InMRT4,
		InMRT5,
		 
		CustomNativeDepth,
		AnisotropicData,
		CustomStencil,
		SceneDepth,
		bGetNormalizedNormal,
		CheckerFromPixelPos(PixelPos));

	return Ret;
}


// @param PixelPos relative to left top of the rendertarget (not viewport)
FGBufferData DecodeGBufferDataSceneTextures(float2 UV, bool bGetNormalizedNormal = true)
{
	uint CustomStencil = 0;
	float CustomNativeDepth = 0;
	float DeviceZ = SampleDeviceZFromSceneTexturesTempCopy(UV);
	float SceneDepth = ConvertFromDeviceZ(DeviceZ);
	float4 AnisotropicData = GBufferFTexture.SampleLevel(GBufferFTextureSampler, UV, 0).xyzw;

	float4 InMRT1 = GBufferATexture.SampleLevel(GBufferATextureSampler, UV, 0).xyzw;
	float4 InMRT2 = GBufferBTexture.SampleLevel(GBufferBTextureSampler, UV, 0).xyzw;
	float4 InMRT3 = GBufferCTexture.SampleLevel(GBufferCTextureSampler, UV, 0).xyzw;
	float4 InMRT4 = GBufferDTexture.SampleLevel(GBufferDTextureSampler, UV, 0).xyzw;
	float4 InMRT5 = GBufferETexture.SampleLevel(GBufferETextureSampler, UV, 0).xyzw;

	FGBufferData Ret = DecodeGBufferDataDirect(InMRT1,
		InMRT2,
		InMRT3,
		InMRT4,
		InMRT5,
		 
		CustomNativeDepth,
		AnisotropicData,
		CustomStencil,
		SceneDepth,
		bGetNormalizedNormal,
		CheckerFromSceneColorUV(UV));

	return Ret;
}


// @param PixelPos relative to left top of the rendertarget (not viewport)
FGBufferData DecodeGBufferDataSceneTexturesLoad(uint2 PixelCoord, bool bGetNormalizedNormal = true)
{
	uint CustomStencil = 0;
	float CustomNativeDepth = 0;
	float DeviceZ = SceneDepthTexture.Load(int3(PixelCoord, 0)).r;
	float SceneDepth = ConvertFromDeviceZ(DeviceZ);
	float4 AnisotropicData = GBufferFTexture.Load(int3(PixelCoord, 0)).xyzw;

	float4 InMRT1 = GBufferATexture.Load(int3(PixelCoord, 0)).xyzw;
	float4 InMRT2 = GBufferBTexture.Load(int3(PixelCoord, 0)).xyzw;
	float4 InMRT3 = GBufferCTexture.Load(int3(PixelCoord, 0)).xyzw;
	float4 InMRT4 = GBufferDTexture.Load(int3(PixelCoord, 0)).xyzw;
	float4 InMRT5 = GBufferETexture.Load(int3(PixelCoord, 0)).xyzw;

	FGBufferData Ret = DecodeGBufferDataDirect(InMRT1,
		InMRT2,
		InMRT3,
		InMRT4,
		InMRT5,
		 
		CustomNativeDepth,
		AnisotropicData,
		CustomStencil,
		SceneDepth,
		bGetNormalizedNormal,
		CheckerFromPixelPos(PixelCoord));

	return Ret;
}


#endif

#endif

#if GBUFFER_LAYOUT == 1

void EncodeGBufferToMRT(inout FPixelShaderOut Out, FGBufferData GBuffer, float QuantizationBias)
{
	float4 MrtFloat1 = 0.0f;
	float4 MrtFloat2 = 0.0f;
	uint4 MrtUint2 = 0;
	float4 MrtFloat3 = 0.0f;
	float2 MrtFloat4 = 0.0f;
	float4 MrtFloat5 = 0.0f;
	float4 MrtFloat6 = 0.0f;

	float3 WorldNormal_Compressed = EncodeNormalHelper(GBuffer.WorldNormal, 0.0f);

	MrtFloat1.x = WorldNormal_Compressed.x;
	MrtFloat1.y = WorldNormal_Compressed.y;
	MrtFloat1.z = WorldNormal_Compressed.z;
	MrtFloat1.w = GBuffer.PerObjectGBufferData.x;
	MrtFloat2.x = GBuffer.Metallic.x;
	MrtFloat2.y = GBuffer.Specular.x;
	MrtFloat2.z = GBuffer.Roughness.x;
	MrtUint2.w |= ((((GBuffer.ShadingModelID.x) >> 0) & 0x0f) << 0);
	MrtUint2.w |= ((((GBuffer.SelectiveOutputMask.x) >> 0) & 0x0f) << 4);
	MrtFloat3.x = GBuffer.BaseColor.x;
	MrtFloat3.y = GBuffer.BaseColor.y;
	MrtFloat3.z = GBuffer.BaseColor.z;
	MrtFloat3.w = GBuffer.GenericAO.x;
	MrtFloat4.x = GBuffer.Velocity.x;
	MrtFloat4.y = GBuffer.Velocity.y;
	MrtFloat6.x = GBuffer.PrecomputedShadowFactors.x;
	MrtFloat6.y = GBuffer.PrecomputedShadowFactors.y;
	MrtFloat6.z = GBuffer.PrecomputedShadowFactors.z;
	MrtFloat6.w = GBuffer.PrecomputedShadowFactors.w;
	MrtFloat5.x = GBuffer.CustomData.x;
	MrtFloat5.y = GBuffer.CustomData.y;
	MrtFloat5.z = GBuffer.CustomData.z;
	MrtFloat5.w = GBuffer.CustomData.w;

	Out.MRT[1] = MrtFloat1;
	Out.MRT[2] = float4(MrtFloat2.x, MrtFloat2.y, MrtFloat2.z, float(MrtUint2.w) / 255.0f);
	Out.MRT[3] = MrtFloat3;
	Out.MRT[4] = float4(MrtFloat4.x, MrtFloat4.y, 0.0f, 0.0f);
	Out.MRT[5] = MrtFloat5;
	Out.MRT[6] = MrtFloat6;
	Out.MRT[7] = float4(0.0f, 0.0f, 0.0f, 0.0f);
}


FGBufferData  DecodeGBufferDataDirect(float4 InMRT1,
	float4 InMRT2,
	float4 InMRT3,
	float2 InMRT4,
	float4 InMRT5,
	float4 InMRT6,
		 
	float CustomNativeDepth,
	float4 AnisotropicData,
	uint CustomStencil,
	float SceneDepth,
	bool bGetNormalizedNormal,
	bool bChecker)
{
	FGBufferData Ret = (FGBufferData)0;
	float3 WorldNormal_Compressed = 0.0f;
	WorldNormal_Compressed.x = InMRT1.x;
	WorldNormal_Compressed.y = InMRT1.y;
	WorldNormal_Compressed.z = InMRT1.z;
	Ret.PerObjectGBufferData.x = InMRT1.w;
	Ret.Metallic.x = InMRT2.x;
	Ret.Specular.x = InMRT2.y;
	Ret.Roughness.x = InMRT2.z;
	Ret.ShadingModelID.x = (((uint((float(InMRT2.w) * 255.0f) + .5f) >> 0) & 0x0f) << 0);
	Ret.SelectiveOutputMask.x = (((uint((float(InMRT2.w) * 255.0f) + .5f) >> 4) & 0x0f) << 0);
	Ret.BaseColor.x = InMRT3.x;
	Ret.BaseColor.y = InMRT3.y;
	Ret.BaseColor.z = InMRT3.z;
	Ret.GenericAO.x = InMRT3.w;
	Ret.Velocity.x = InMRT4.x;
	Ret.Velocity.y = InMRT4.y;
	Ret.PrecomputedShadowFactors.x = InMRT6.x;
	Ret.PrecomputedShadowFactors.y = InMRT6.y;
	Ret.PrecomputedShadowFactors.z = InMRT6.z;
	Ret.PrecomputedShadowFactors.w = InMRT6.w;
	Ret.CustomData.x = InMRT5.x;
	Ret.CustomData.y = InMRT5.y;
	Ret.CustomData.z = InMRT5.z;
	Ret.CustomData.w = InMRT5.w;
	
	Ret.WorldNormal = DecodeNormalHelper(WorldNormal_Compressed);
	Ret.WorldTangent = AnisotropicData.xyz;
	Ret.Anisotropy = AnisotropicData.w;

	GBufferPostDecode(Ret,bChecker,bGetNormalizedNormal);

	Ret.CustomDepth = ConvertFromDeviceZ(CustomNativeDepth);
	Ret.CustomStencil = CustomStencil;
	Ret.Depth = SceneDepth;
	

	return Ret;
}


#if FEATURE_LEVEL >= FEATURE_LEVEL_SM5

// @param PixelPos relative to left top of the rendertarget (not viewport)
FGBufferData DecodeGBufferDataUV(float2 UV, bool bGetNormalizedNormal = true)
{
	float CustomNativeDepth = Texture2DSampleLevel(SceneTexturesStruct.CustomDepthTexture, SceneTexturesStruct_CustomDepthTextureSampler, UV, 0).r;
	int2 IntUV = (int2)trunc(UV * View.BufferSizeAndInvSize.xy * View.BufferToSceneTextureScale.xy);
	uint CustomStencil = SceneTexturesStruct.CustomStencilTexture.Load(int3(IntUV, 0)) STENCIL_COMPONENT_SWIZZLE;
	float SceneDepth = CalcSceneDepth(UV);
	float4 AnisotropicData = Texture2DSampleLevel(SceneTexturesStruct.GBufferFTexture, SceneTexturesStruct_GBufferFTextureSampler, UV, 0).xyzw;

	float4 InMRT1 = Texture2DSampleLevel(SceneTexturesStruct.GBufferATexture, SceneTexturesStruct_GBufferATextureSampler, UV, 0).xyzw;
	float4 InMRT2 = Texture2DSampleLevel(SceneTexturesStruct.GBufferBTexture, SceneTexturesStruct_GBufferBTextureSampler, UV, 0).xyzw;
	float4 InMRT3 = Texture2DSampleLevel(SceneTexturesStruct.GBufferCTexture, SceneTexturesStruct_GBufferCTextureSampler, UV, 0).xyzw;
	float2 InMRT4 = Texture2DSampleLevel(SceneTexturesStruct.GBufferVelocityTexture, SceneTexturesStruct_GBufferVelocityTextureSampler, UV, 0).xy;
	float4 InMRT5 = Texture2DSampleLevel(SceneTexturesStruct.GBufferDTexture, SceneTexturesStruct_GBufferDTextureSampler, UV, 0).xyzw;
	float4 InMRT6 = Texture2DSampleLevel(SceneTexturesStruct.GBufferETexture, SceneTexturesStruct_GBufferETextureSampler, UV, 0).xyzw;

	FGBufferData Ret = DecodeGBufferDataDirect(InMRT1,
		InMRT2,
		InMRT3,
		InMRT4,
		InMRT5,
		InMRT6,
		 
		CustomNativeDepth,
		AnisotropicData,
		CustomStencil,
		SceneDepth,
		bGetNormalizedNormal,
		CheckerFromSceneColorUV(UV));

	return Ret;
}


// @param PixelPos relative to left top of the rendertarget (not viewport)
FGBufferData DecodeGBufferDataUint(uint2 PixelPos, bool bGetNormalizedNormal = true)
{
	float CustomNativeDepth = SceneTexturesStruct.CustomDepthTexture.Load(int3(PixelPos, 0)).r;
	uint CustomStencil = SceneTexturesStruct.CustomStencilTexture.Load(int3(PixelPos, 0)) STENCIL_COMPONENT_SWIZZLE;
	float SceneDepth = CalcSceneDepth(PixelPos);
	float4 AnisotropicData = SceneTexturesStruct.GBufferFTexture.Load(int3(PixelPos, 0)).xyzw;

	float4 InMRT1 = SceneTexturesStruct.GBufferATexture.Load(int3(PixelPos, 0)).xyzw;
	float4 InMRT2 = SceneTexturesStruct.GBufferBTexture.Load(int3(PixelPos, 0)).xyzw;
	float4 InMRT3 = SceneTexturesStruct.GBufferCTexture.Load(int3(PixelPos, 0)).xyzw;
	float2 InMRT4 = SceneTexturesStruct.GBufferVelocityTexture.Load(int3(PixelPos, 0)).xy;
	float4 InMRT5 = SceneTexturesStruct.GBufferDTexture.Load(int3(PixelPos, 0)).xyzw;
	float4 InMRT6 = SceneTexturesStruct.GBufferETexture.Load(int3(PixelPos, 0)).xyzw;

	FGBufferData Ret = DecodeGBufferDataDirect(InMRT1,
		InMRT2,
		InMRT3,
		InMRT4,
		InMRT5,
		InMRT6,
		 
		CustomNativeDepth,
		AnisotropicData,
		CustomStencil,
		SceneDepth,
		bGetNormalizedNormal,
		CheckerFromPixelPos(PixelPos));

	return Ret;
}


// @param PixelPos relative to left top of the rendertarget (not viewport)
FGBufferData DecodeGBufferDataSceneTextures(float2 UV, bool bGetNormalizedNormal = true)
{
	uint CustomStencil = 0;
	float CustomNativeDepth = 0;
	float DeviceZ = SampleDeviceZFromSceneTexturesTempCopy(UV);
	float SceneDepth = ConvertFromDeviceZ(DeviceZ);
	float4 AnisotropicData = GBufferFTexture.SampleLevel(GBufferFTextureSampler, UV, 0).xyzw;

	float4 InMRT1 = GBufferATexture.SampleLevel(GBufferATextureSampler, UV, 0).xyzw;
	float4 InMRT2 = GBufferBTexture.SampleLevel(GBufferBTextureSampler, UV, 0).xyzw;
	float4 InMRT3 = GBufferCTexture.SampleLevel(GBufferCTextureSampler, UV, 0).xyzw;
	float2 InMRT4 = GBufferVelocityTexture.SampleLevel(GBufferVelocityTextureSampler, UV, 0).xy;
	float4 InMRT5 = GBufferDTexture.SampleLevel(GBufferDTextureSampler, UV, 0).xyzw;
	float4 InMRT6 = GBufferETexture.SampleLevel(GBufferETextureSampler, UV, 0).xyzw;

	FGBufferData Ret = DecodeGBufferDataDirect(InMRT1,
		InMRT2,
		InMRT3,
		InMRT4,
		InMRT5,
		InMRT6,
		 
		CustomNativeDepth,
		AnisotropicData,
		CustomStencil,
		SceneDepth,
		bGetNormalizedNormal,
		CheckerFromSceneColorUV(UV));

	return Ret;
}


// @param PixelPos relative to left top of the rendertarget (not viewport)
FGBufferData DecodeGBufferDataSceneTexturesLoad(uint2 PixelCoord, bool bGetNormalizedNormal = true)
{
	uint CustomStencil = 0;
	float CustomNativeDepth = 0;
	float DeviceZ = SceneDepthTexture.Load(int3(PixelCoord, 0)).r;
	float SceneDepth = ConvertFromDeviceZ(DeviceZ);
	float4 AnisotropicData = GBufferFTexture.Load(int3(PixelCoord, 0)).xyzw;

	float4 InMRT1 = GBufferATexture.Load(int3(PixelCoord, 0)).xyzw;
	float4 InMRT2 = GBufferBTexture.Load(int3(PixelCoord, 0)).xyzw;
	float4 InMRT3 = GBufferCTexture.Load(int3(PixelCoord, 0)).xyzw;
	float2 InMRT4 = GBufferVelocityTexture.Load(int3(PixelCoord, 0)).xy;
	float4 InMRT5 = GBufferDTexture.Load(int3(PixelCoord, 0)).xyzw;
	float4 InMRT6 = GBufferETexture.Load(int3(PixelCoord, 0)).xyzw;

	FGBufferData Ret = DecodeGBufferDataDirect(InMRT1,
		InMRT2,
		InMRT3,
		InMRT4,
		InMRT5,
		InMRT6,
		 
		CustomNativeDepth,
		AnisotropicData,
		CustomStencil,
		SceneDepth,
		bGetNormalizedNormal,
		CheckerFromPixelPos(PixelCoord));

	return Ret;
}


#endif

#endif

