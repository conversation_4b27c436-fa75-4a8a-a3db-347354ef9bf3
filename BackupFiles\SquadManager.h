#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "AI/SquadMateAIController.h"
#include "GameplayTagContainer.h"
#include "SquadManager.generated.h"

class ASquadMateCharacter;
class USquadRoleComponent;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSquadMemberAdded, ASquadMateAIController*, NewMember, ESquadRole, AssignedRole);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSquadMemberRemoved, ASquadMateAIController*, RemovedMember, ESquadRole, PreviousRole);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnSquadRoleChanged, ASquadMateAIController*, Member, ESquadRole, OldRole, ESquadRole, NewRole);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnSquadFormationChanged, const TArray<ASquadMateAIController*>&, SquadMembers);

UENUM(BlueprintType)
enum class ESquadFormation : uint8
{
    Line            UMETA(DisplayName = "Line Formation"),
    Wedge           UMETA(DisplayName = "Wedge Formation"),
    Column          UMETA(DisplayName = "Column Formation"),
    Diamond         UMETA(DisplayName = "Diamond Formation"),
    Spread          UMETA(DisplayName = "Spread Formation"),
    Custom          UMETA(DisplayName = "Custom Formation")
};

UENUM(BlueprintType)
enum class ESquadTactic : uint8
{
    Assault         UMETA(DisplayName = "Assault"),
    Defend          UMETA(DisplayName = "Defend"),
    Flank           UMETA(DisplayName = "Flank"),
    Retreat         UMETA(DisplayName = "Retreat"),
    Hold            UMETA(DisplayName = "Hold Position"),
    Advance         UMETA(DisplayName = "Advance"),
    Regroup         UMETA(DisplayName = "Regroup")
};

USTRUCT(BlueprintType)
struct FSquadFormationData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    ESquadFormation FormationType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FVector> RelativePositions;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float FormationSpacing = 300.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bMaintainFormation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float FormationTolerance = 100.0f;

    FSquadFormationData()
    {
        FormationType = ESquadFormation::Line;
        FormationSpacing = 300.0f;
        bMaintainFormation = true;
        FormationTolerance = 100.0f;
    }
};

USTRUCT(BlueprintType)
struct FSquadObjective
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString ObjectiveName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FVector TargetLocation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    AActor* TargetActor = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    ESquadTactic RequiredTactic;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float Priority = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float CompletionRadius = 200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIsCompleted = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FGameplayTagContainer ObjectiveTags;

    FSquadObjective()
    {
        ObjectiveName = TEXT("Default Objective");
        TargetLocation = FVector::ZeroVector;
        TargetActor = nullptr;
        RequiredTactic = ESquadTactic::Advance;
        Priority = 1.0f;
        CompletionRadius = 200.0f;
        bIsCompleted = false;
    }
};

UCLASS(BlueprintType, Blueprintable)
class SQUADMATEAI_API ASquadManager : public AActor
{
    GENERATED_BODY()

public:
    ASquadManager();

protected:
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

    // Squad Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Squad")
    int32 MaxSquadSize = 5;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Squad")
    int32 TeamID = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Squad")
    FString SquadName = TEXT("Alpha Squad");

    UPROPERTY(BlueprintReadOnly, Category = "Squad")
    TArray<ASquadMateAIController*> SquadMembers;

    UPROPERTY(BlueprintReadOnly, Category = "Squad")
    TMap<ESquadRole, ASquadMateAIController*> RoleAssignments;

    // Formation System
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    ESquadFormation CurrentFormation = ESquadFormation::Line;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Formation")
    FSquadFormationData FormationData;

    UPROPERTY(BlueprintReadOnly, Category = "Formation")
    FVector FormationCenter;

    UPROPERTY(BlueprintReadOnly, Category = "Formation")
    FRotator FormationRotation;

    // Tactical System
    UPROPERTY(BlueprintReadWrite, Category = "Tactics")
    ESquadTactic CurrentTactic = ESquadTactic::Hold;

    UPROPERTY(BlueprintReadOnly, Category = "Tactics")
    TArray<FSquadObjective> ActiveObjectives;

    UPROPERTY(BlueprintReadOnly, Category = "Tactics")
    FSquadObjective CurrentObjective;

    // Communication System
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Communication")
    float CommunicationRange = 2000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Communication")
    bool bEnableVoiceCallouts = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Communication")
    float CalloutCooldown = 3.0f;

    // Squad Leader
    UPROPERTY(BlueprintReadOnly, Category = "Leadership")
    ASquadMateAIController* SquadLeader = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Leadership")
    bool bAutoAssignLeader = true;

    // Performance Tracking
    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    FSquadMateStats SquadStats;

    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    float SquadCohesion = 1.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Stats")
    float SquadEffectiveness = 1.0f;

    // Events
    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnSquadMemberAdded OnSquadMemberAdded;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnSquadMemberRemoved OnSquadMemberRemoved;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnSquadRoleChanged OnSquadRoleChanged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnSquadFormationChanged OnSquadFormationChanged;

public:
    // Squad Management
    UFUNCTION(BlueprintCallable, Category = "Squad")
    bool RegisterAgent(ASquadMateAIController* Agent);

    UFUNCTION(BlueprintCallable, Category = "Squad")
    bool UnregisterAgent(ASquadMateAIController* Agent);

    UFUNCTION(BlueprintCallable, Category = "Squad")
    void DisbandSquad();

    UFUNCTION(BlueprintCallable, Category = "Squad")
    int32 GetSquadSize() const { return SquadMembers.Num(); }

    UFUNCTION(BlueprintCallable, Category = "Squad")
    TArray<ASquadMateAIController*> GetSquadMembers() const { return SquadMembers; }

    UFUNCTION(BlueprintCallable, Category = "Squad")
    bool IsSquadFull() const { return SquadMembers.Num() >= MaxSquadSize; }

    // Role Management
    UFUNCTION(BlueprintCallable, Category = "Roles")
    bool AssignRole(ASquadMateAIController* Agent, ESquadRole Role);

    UFUNCTION(BlueprintCallable, Category = "Roles")
    ESquadRole GetAgentRole(ASquadMateAIController* Agent) const;

    UFUNCTION(BlueprintCallable, Category = "Roles")
    ASquadMateAIController* GetAgentWithRole(ESquadRole Role) const;

    UFUNCTION(BlueprintCallable, Category = "Roles")
    void AutoAssignRoles();

    UFUNCTION(BlueprintCallable, Category = "Roles")
    bool IsRoleAvailable(ESquadRole Role) const;

    // Formation Management
    UFUNCTION(BlueprintCallable, Category = "Formation")
    void SetFormation(ESquadFormation NewFormation);

    UFUNCTION(BlueprintCallable, Category = "Formation")
    void UpdateFormationCenter(const FVector& NewCenter);

    UFUNCTION(BlueprintCallable, Category = "Formation")
    void UpdateFormationRotation(const FRotator& NewRotation);

    UFUNCTION(BlueprintCallable, Category = "Formation")
    FVector GetFormationPositionForAgent(ASquadMateAIController* Agent) const;

    UFUNCTION(BlueprintCallable, Category = "Formation")
    void MaintainFormation();

    // Tactical Commands
    UFUNCTION(BlueprintCallable, Category = "Tactics")
    void SetSquadTactic(ESquadTactic NewTactic);

    UFUNCTION(BlueprintCallable, Category = "Tactics")
    void AddObjective(const FSquadObjective& Objective);

    UFUNCTION(BlueprintCallable, Category = "Tactics")
    void RemoveObjective(const FString& ObjectiveName);

    UFUNCTION(BlueprintCallable, Category = "Tactics")
    void SetCurrentObjective(const FString& ObjectiveName);

    UFUNCTION(BlueprintCallable, Category = "Tactics")
    void MoveToLocation(const FVector& TargetLocation);

    UFUNCTION(BlueprintCallable, Category = "Tactics")
    void AttackTarget(AActor* Target);

    UFUNCTION(BlueprintCallable, Category = "Tactics")
    void DefendLocation(const FVector& DefenseLocation);

    // Communication
    UFUNCTION(BlueprintCallable, Category = "Communication")
    void BroadcastCallout(const FString& Message, ASquadMateAIController* Sender = nullptr);

    UFUNCTION(BlueprintCallable, Category = "Communication")
    void ReportEnemyContact(const FVector& EnemyLocation, ASquadMateAIController* Reporter);

    UFUNCTION(BlueprintCallable, Category = "Communication")
    void RequestSupport(ASquadMateAIController* Requester, const FVector& Location);

    UFUNCTION(BlueprintCallable, Category = "Communication")
    void ReportCasualty(ASquadMateAIController* CasualtyAgent);

    // Leadership
    UFUNCTION(BlueprintCallable, Category = "Leadership")
    void SetSquadLeader(ASquadMateAIController* NewLeader);

    UFUNCTION(BlueprintCallable, Category = "Leadership")
    ASquadMateAIController* GetSquadLeader() const { return SquadLeader; }

    UFUNCTION(BlueprintCallable, Category = "Leadership")
    void PromoteNewLeader();

    // Squad Analysis
    UFUNCTION(BlueprintCallable, Category = "Analysis")
    float CalculateSquadCohesion() const;

    UFUNCTION(BlueprintCallable, Category = "Analysis")
    float CalculateSquadEffectiveness() const;

    UFUNCTION(BlueprintCallable, Category = "Analysis")
    FVector GetSquadCenterOfMass() const;

    UFUNCTION(BlueprintCallable, Category = "Analysis")
    float GetAverageSquadHealth() const;

    UFUNCTION(BlueprintCallable, Category = "Analysis")
    int32 GetActiveSquadMembers() const;

    // Utility Functions
    UFUNCTION(BlueprintCallable, Category = "Utility")
    TArray<ASquadMateAIController*> GetNearbySquadMembers(const FVector& Location, float Radius) const;

    UFUNCTION(BlueprintCallable, Category = "Utility")
    ASquadMateAIController* GetClosestSquadMember(const FVector& Location) const;

    UFUNCTION(BlueprintCallable, Category = "Utility")
    bool IsLocationWithinSquad(const FVector& Location, float Tolerance = 500.0f) const;

protected:
    // Internal Methods
    void InitializeSquad();
    void UpdateSquadStatus(float DeltaTime);
    void ProcessObjectives(float DeltaTime);
    void UpdateFormationPositions();
    void ManageSquadCommunication(float DeltaTime);

    // Role Assignment Logic
    ESquadRole DetermineOptimalRole(ASquadMateAIController* Agent) const;
    void RebalanceRoles();
    bool ValidateRoleAssignment(ASquadMateAIController* Agent, ESquadRole Role) const;

    // Formation Calculations
    TArray<FVector> CalculateFormationPositions(ESquadFormation Formation, int32 MemberCount) const;
    FVector CalculateRelativePosition(int32 MemberIndex, ESquadFormation Formation, float Spacing) const;

    // Tactical Decision Making
    void EvaluateTacticalSituation();
    ESquadTactic DetermineBestTactic() const;
    void ExecuteTactic(ESquadTactic Tactic);

    // Communication Management
    TMap<ASquadMateAIController*, float> LastCalloutTimes;
    void ProcessCallout(const FString& Message, ASquadMateAIController* Sender);

private:
    // Internal State
    float LastUpdateTime = 0.0f;
    float LastFormationUpdate = 0.0f;
    float LastTacticalEvaluation = 0.0f;
    
    // Performance optimization
    const float UpdateFrequency = 0.2f;
    const float FormationUpdateFrequency = 0.5f;
    const float TacticalEvaluationFrequency = 1.0f;
};
