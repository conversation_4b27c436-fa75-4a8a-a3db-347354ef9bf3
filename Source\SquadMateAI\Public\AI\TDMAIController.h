#pragma once

#include "CoreMinimal.h"
#include "AIController.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "Perception/AIPerceptionComponent.h"
#include "Components/DecisionLoggerComponent.h"
#include "GameplayTagContainer.h"
#include "Engine/DataTable.h"
#include "TDMAIController.generated.h"

class ATDMGameMode;
class ATDMMatchManager;
class UVictorCharacter;
class UTDMWeaponSystem;
class USquadRoleComponent;
class UHealthComponent;
class UInventoryComponent;

// PUBGM TDM Specific Enums
UENUM(BlueprintType)
enum class ETDMTacticState : uint8
{
    Patrol      UMETA(DisplayName = "Patrol"),
    Engage      UMETA(DisplayName = "Engage"),
    Flank       UMETA(DisplayName = "Flank"),
    Hold        UMETA(DisplayName = "Hold"),
    Revive      UMETA(DisplayName = "Revive"),
    Retreat     UMETA(DisplayName = "Retreat"),
    Suppress    UMETA(DisplayName = "Suppress"),
    Peek        UMETA(DisplayName = "Peek"),
    Slide       UMETA(DisplayName = "Slide")
};

UENUM(BlueprintType)
enum class ETDMRole : uint8
{
    Assault     UMETA(DisplayName = "Assault"),
    Support     UMETA(DisplayName = "Support"),
    Sniper      UMETA(DisplayName = "Sniper"),
    Flanker     UMETA(DisplayName = "Flanker"),
    Entry       UMETA(DisplayName = "Entry")
};

UENUM(BlueprintType)
enum class ETDMWeaponType : uint8
{
    AR_M416     UMETA(DisplayName = "M416"),
    AR_AKM      UMETA(DisplayName = "AKM"),
    AR_SCAR     UMETA(DisplayName = "SCAR-L"),
    SMG_UMP45   UMETA(DisplayName = "UMP45"),
    SMG_UZI     UMETA(DisplayName = "UZI"),
    SMG_VECTOR  UMETA(DisplayName = "Vector"),
    SHOTGUN_S12K UMETA(DisplayName = "S12K"),
    SNIPER_SKS  UMETA(DisplayName = "SKS"),
    SNIPER_KAR98 UMETA(DisplayName = "Kar98"),
    SNIPER_M24  UMETA(DisplayName = "M24")
};

UENUM(BlueprintType)
enum class ETDMLane : uint8
{
    Left        UMETA(DisplayName = "Left"),
    Center      UMETA(DisplayName = "Center"),
    Right       UMETA(DisplayName = "Right"),
    Spawn       UMETA(DisplayName = "Spawn")
};

// PUBGM TDM Decision Structure
USTRUCT(BlueprintType)
struct FTDMDecisionNode
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString NodeName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 Priority;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FString> Conditions;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<FString> Actions;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float CooldownTime;

    FTDMDecisionNode()
    {
        Priority = 0;
        CooldownTime = 0.0f;
    }
};

// TDM Loadout Configuration
USTRUCT(BlueprintType)
struct FTDMLoadout
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    ETDMWeaponType PrimaryWeapon;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    ETDMWeaponType SecondaryWeapon;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bHasMelee;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    ETDMRole PreferredRole;

    FTDMLoadout()
    {
        PrimaryWeapon = ETDMWeaponType::AR_M416;
        SecondaryWeapon = ETDMWeaponType::SMG_UMP45;
        bHasMelee = true;
        PreferredRole = ETDMRole::Assault;
    }
};

// Events and Delegates
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTDMTacticChanged, ETDMTacticState, OldState, ETDMTacticState, NewState);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnTDMEnemyDetected, AActor*, Enemy);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnTDMKillConfirmed, AActor*, Victim);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnTDMDeathEvent, AActor*, Killer);

/**
 * Enhanced AI Controller specifically designed for PUBG Mobile 5v5 TDM mechanics
 * Implements production-ready tactical AI with role-based coordination
 */
UCLASS(BlueprintType, Blueprintable)
class SQUADMATEAI_API ATDMAIController : public AAIController
{
    GENERATED_BODY()

public:
    ATDMAIController();

protected:
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;
    virtual void OnPossess(APawn* InPawn) override;

public:
    // Core AI Components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AI Components")
    class UBehaviorTreeComponent* BehaviorTreeComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AI Components")
    class UBlackboardComponent* BlackboardComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AI Components")
    class UAIPerceptionComponent* AIPerceptionComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AI Components")
    class UDecisionLoggerComponent* DecisionLogger;

    // TDM Specific Components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "TDM Components")
    class UTDMWeaponSystem* WeaponSystem;

    // Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "TDM Configuration")
    class UBehaviorTree* TDMBehaviorTree;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "TDM Configuration")
    FString JSONConfigurationPath;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "TDM Configuration")
    FTDMLoadout CurrentLoadout;

    // Current State
    UPROPERTY(BlueprintReadOnly, Category = "TDM State")
    ETDMTacticState CurrentTactic;

    UPROPERTY(BlueprintReadOnly, Category = "TDM State")
    ETDMRole AssignedRole;

    UPROPERTY(BlueprintReadOnly, Category = "TDM State")
    ETDMLane CurrentLane;

    UPROPERTY(BlueprintReadOnly, Category = "TDM State")
    int32 TeamID;

    UPROPERTY(BlueprintReadOnly, Category = "TDM State")
    int32 KillCount;

    UPROPERTY(BlueprintReadOnly, Category = "TDM State")
    int32 DeathCount;

    // Performance Metrics
    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float AccuracyPercentage;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float AverageReactionTime;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float TacticalEfficiency;

    // Events
    UPROPERTY(BlueprintAssignable, Category = "TDM Events")
    FOnTDMTacticChanged OnTacticStateChanged;

    UPROPERTY(BlueprintAssignable, Category = "TDM Events")
    FOnTDMEnemyDetected OnEnemyDetected;

    UPROPERTY(BlueprintAssignable, Category = "TDM Events")
    FOnTDMKillConfirmed OnKillConfirmed;

    UPROPERTY(BlueprintAssignable, Category = "TDM Events")
    FOnTDMDeathEvent OnDeathEvent;

    // Public Interface
    UFUNCTION(BlueprintCallable, Category = "TDM AI")
    void SetTacticState(ETDMTacticState NewState);

    UFUNCTION(BlueprintCallable, Category = "TDM AI")
    void SetRole(ETDMRole NewRole);

    UFUNCTION(BlueprintCallable, Category = "TDM AI")
    void SetLane(ETDMLane NewLane);

    UFUNCTION(BlueprintCallable, Category = "TDM AI")
    void LoadJSONConfiguration(const FString& ConfigPath);

    UFUNCTION(BlueprintCallable, Category = "TDM AI")
    void SwitchLoadout(const FTDMLoadout& NewLoadout);

    UFUNCTION(BlueprintCallable, Category = "TDM AI")
    void OnKill(AActor* Victim);

    UFUNCTION(BlueprintCallable, Category = "TDM AI")
    void OnDeath(AActor* Killer);

    UFUNCTION(BlueprintCallable, Category = "TDM AI")
    void OnRespawn();

    // Getters
    UFUNCTION(BlueprintPure, Category = "TDM AI")
    ETDMTacticState GetCurrentTactic() const { return CurrentTactic; }

    UFUNCTION(BlueprintPure, Category = "TDM AI")
    ETDMRole GetAssignedRole() const { return AssignedRole; }

    UFUNCTION(BlueprintPure, Category = "TDM AI")
    float GetKDRatio() const { return DeathCount > 0 ? (float)KillCount / DeathCount : KillCount; }

    UFUNCTION(BlueprintPure, Category = "TDM AI")
    bool IsInCombat() const;

    UFUNCTION(BlueprintPure, Category = "TDM AI")
    bool HasTarget() const;

    UFUNCTION(BlueprintPure, Category = "TDM AI")
    AActor* GetCurrentTarget() const;

protected:
    // Internal State
    float LastCombatTime;
    float LastDecisionTime;
    float SpawnProtectionEndTime;
    TArray<FTDMDecisionNode> DecisionTree;
    TMap<FString, float> ActionCooldowns;

    // Blackboard Keys
    FName TargetActorKey;
    FName TacticStateKey;
    FName HasLineOfSightKey;
    FName IsInCombatKey;
    FName CoverLocationKey;
    FName TeamMatesKey;
    FName EnemiesKey;
    FName CurrentLaneKey;
    FName IsUnderFireKey;
    FName AmmoLowKey;
    FName HealthLowKey;
    FName ReviveTargetKey;
    FName IsRevivingKey;
    FName SpawnProtectedKey;

    // Internal Methods
    void InitializeAIComponents();
    void InitializePerception();
    void InitializeBlackboard();
    void StartBehaviorTree();
    void LoadDefaultConfiguration();

    // Decision Making
    void UpdateDecisionMaking(float DeltaTime);
    ETDMTacticState DetermineBestTactic();
    void ExecuteTactic(ETDMTacticState Tactic);
    bool EvaluateCondition(const FString& Condition);
    void ExecuteAction(const FString& Action);

    // Perception Callbacks
    UFUNCTION()
    void OnPerceptionUpdated(const TArray<AActor*>& UpdatedActors);

    // Utility Functions
    void UpdateBlackboardValues();
    void UpdateCombatState();
    void UpdateTeamCoordination();
    void ProcessEnemyDetection(AActor* Enemy);
    void ProcessAllyDetection(AActor* Ally);

    // Combat Logic
    bool CanEngageTarget(AActor* Target) const;
    bool ShouldRetreat() const;
    bool ShouldSeekCover() const;
    bool ShouldFlank() const;
    bool ShouldReviveAlly() const;
    bool ShouldSuppressFire() const;
    bool ShouldPeek() const;

    // PUBGM Specific Mechanics
    void HandleSpawnProtection();
    void UpdateInfiniteAmmo();
    void ProcessSlideMovement();
    void HandlePeekMechanics();
    void UpdateLaneControl();
};
