#pragma once

#include "CoreMinimal.h"
#include "BehaviorTree/BTTaskNode.h"
#include "BTTask_FireWeapon.generated.h"

UENUM(BlueprintType)
enum class EFireMode : uint8
{
    SingleShot      UMETA(DisplayName = "Single Shot"),
    BurstFire       UMETA(DisplayName = "Burst Fire"),
    FullAuto        UMETA(DisplayName = "Full Auto"),
    Suppressive     UMETA(DisplayName = "Suppressive Fire")
};

/**
 * Behavior Tree task for weapon firing
 * Handles aiming, firing patterns, and ammo management
 */
UCLASS(BlueprintType, meta=(DisplayName="Fire Weapon"))
class SQUADMATEAI_API UBTTask_FireWeapon : public UBTTaskNode
{
    GENERATED_BODY()

public:
    UBTTask_FireWeapon();

protected:
    virtual EBTNodeResult::Type ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;
    virtual EBTNodeResult::Type AbortTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;
    virtual void TickTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaSeconds) override;
    virtual FString GetStaticDescription() const override;
    virtual uint16 GetInstanceMemorySize() const override;

    // Blackboard Keys
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector TargetActorKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector HasLineOfSightKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector AmmoCountKey;

    // Fire Control Settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Control")
    EFireMode FireMode = EFireMode::BurstFire;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Control")
    float FireRate = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Control")
    int32 BurstSize = 3;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Control")
    float BurstCooldown = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Control")
    float MaxFireDuration = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Control")
    float AccuracyModifier = 0.8f;

    // Engagement Parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Engagement")
    float MinEngagementRange = 50.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Engagement")
    float MaxEngagementRange = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Engagement")
    float OptimalRange = 400.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Engagement")
    bool bRequireLineOfSight = true;

    // Animation and Effects
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    class UAnimMontage* FireMontage;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    class UParticleSystem* MuzzleFlashEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    class USoundBase* FireSound;

    // Debug Options
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bDrawDebugInfo = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bLogFireEvents = true;

public:
    // Static utility functions
    UFUNCTION(BlueprintCallable, Category = "Fire Weapon", CallInEditor = true)
    static bool CanFireAtTarget(AActor* Shooter, AActor* Target, float MaxRange = 1000.0f);

    UFUNCTION(BlueprintCallable, Category = "Fire Weapon", CallInEditor = true)
    static float CalculateAccuracy(AActor* Shooter, AActor* Target, float BaseAccuracy = 0.8f);

protected:
    // Core firing logic
    bool InitializeFiring(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    void UpdateFiring(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    void CompleteFiring(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);

    // Fire execution
    void ExecuteSingleShot(UBehaviorTreeComponent& OwnerComp, const FVector& AimPoint);
    void ExecuteBurstFire(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    void ExecuteFullAuto(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);

    // Aiming and targeting
    FVector CalculateAimPoint(UBehaviorTreeComponent& OwnerComp, AActor* Target);
    FVector ApplyAccuracySpread(const FVector& BaseAimPoint, float Accuracy);
    bool ValidateTarget(UBehaviorTreeComponent& OwnerComp, AActor* Target);

    // Weapon management
    bool HasSufficientAmmo(UBehaviorTreeComponent& OwnerComp);
    void ConsumeAmmo(UBehaviorTreeComponent& OwnerComp, int32 Amount = 1);
    bool CanFireWeapon(UBehaviorTreeComponent& OwnerComp);

    // Effects and feedback
    void PlayFireEffects(AActor* Shooter, const FVector& MuzzleLocation);
    void PlayFireAnimation(AActor* Shooter);
    void CreateBulletTrail(const FVector& Start, const FVector& End);

    // Utility functions
    float GetDistanceToTarget(AActor* From, AActor* To);
    bool HasLineOfSightToTarget(AActor* From, AActor* To);
    FVector GetWeaponMuzzleLocation(AActor* Actor);

    // Debug and logging
    void LogFireEvent(const FString& Event, UBehaviorTreeComponent& OwnerComp, AActor* Target = nullptr);
    void DrawDebugFireInfo(UWorld* World, const FVector& Start, const FVector& End, bool bHit);

private:
    // Task memory structure
    struct FBTTask_FireWeaponMemory
    {
        TWeakObjectPtr<AActor> CurrentTarget;
        float FireStartTime = 0.0f;
        float LastShotTime = 0.0f;
        float LastBurstTime = 0.0f;
        int32 ShotsFiredInBurst = 0;
        int32 TotalShotsFired = 0;
        bool bIsFiring = false;
        bool bInBurstCooldown = false;
        float CurrentAccuracy = 1.0f;
    };

    // Memory management
    void InitializeTaskMemory(uint8* NodeMemory);
    void CleanupTaskMemory(uint8* NodeMemory);
    FBTTask_FireWeaponMemory* GetTaskMemory(uint8* NodeMemory);
};
