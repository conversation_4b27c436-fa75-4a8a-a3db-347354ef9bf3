#pragma once

#include "CoreMinimal.h"
#include "BehaviorTree/BTTaskNode.h"
#include "AI/SquadMateAIController.h"
#include "Components/HealthComponent.h"
#include "Components/ReviveComponent.h"
#include "Engine/DataTable.h"
#include "GameplayTagContainer.h"  // UE 5.6 Enhanced Gameplay Tags
#include "StructUtils/InstancedStruct.h"  // UE 5.6 Instanced Structs
#include "StateTree/StateTreeTypes.h"  // UE 5.6 State Tree integration
#include "BTTask_ReviveAlly_Enhanced.generated.h"

UENUM(BlueprintType)
enum class EReviveResult : uint8
{
    Success         UMETA(DisplayName = "Success"),
    Failed          UMETA(DisplayName = "Failed"),
    Interrupted     UMETA(DisplayName = "Interrupted"),
    TargetDead      UMETA(DisplayName = "Target Dead"),
    TooFarAway      UMETA(DisplayName = "Too Far Away"),
    UnderFire       UMETA(DisplayName = "Under Fire"),
    NoTarget        UMETA(DisplayName = "No Target"),
    InsufficientHealth UMETA(DisplayName = "Insufficient Health"),
    TeammateBusy    UMETA(DisplayName = "Teammate Busy"),
    EnvironmentUnsafe UMETA(DisplayName = "Environment Unsafe")
};

UENUM(BlueprintType)
enum class EReviveApproach : uint8
{
    Direct          UMETA(DisplayName = "Direct"),
    Cautious        UMETA(DisplayName = "Cautious"),
    Stealth         UMETA(DisplayName = "Stealth"),
    CoverToCover    UMETA(DisplayName = "Cover to Cover"),
    TeamCoordinated UMETA(DisplayName = "Team Coordinated")
};

USTRUCT(BlueprintType)
struct FReviveConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Timing")
    float BaseReviveTime = 3.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Timing")
    float RoleModifier = 1.0f; // Support role gets 0.8x, others 1.0x

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Safety")
    float SafetyRadius = 500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Safety")
    float MinHealthToRevive = 30.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Positioning")
    float MaxReviveDistance = 200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Positioning")
    bool bRequireCover = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Communication")
    bool bCallForCover = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Communication")
    bool bBroadcastProgress = true;

    // UE 5.6 Enhanced Features
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UE5.6 Features")
    FGameplayTagContainer RequiredTags;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UE5.6 Features")
    FGameplayTagContainer BlockingTags;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UE5.6 Features")
    bool bUseStateTreeIntegration = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UE5.6 Features")
    bool bUseMassEntityOptimization = false;
};

/**
 * Enhanced Behavior Tree task for reviving downed allies
 * Features: Role-based timing, team coordination, advanced safety checks,
 * multiple approach strategies, and comprehensive communication system
 */
UCLASS(BlueprintType, meta=(DisplayName="Revive Ally Enhanced"))
class SQUADMATEAI_API UBTTask_ReviveAlly_Enhanced : public UBTTaskNode
{
    GENERATED_BODY()

public:
    UBTTask_ReviveAlly_Enhanced();

protected:
    virtual EBTNodeResult::Type ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;
    virtual EBTNodeResult::Type AbortTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;
    virtual void TickTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaSeconds) override;
    virtual void OnTaskFinished(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EBTNodeResult::Type TaskResult) override;
    virtual FString GetStaticDescription() const override;

    // Blackboard Keys
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector ReviveTargetKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector IsRevivingKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector IsUnderFireKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector SquadRoleKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector CoverLocationKey;

    // Enhanced Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FReviveConfiguration ReviveConfig;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    EReviveApproach ApproachStrategy = EReviveApproach::Cautious;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bAdaptToThreatLevel = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bUseTeamCoordination = true;

    // Role-Based Modifiers
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Role Modifiers")
    TMap<FString, float> RoleReviveTimeModifiers;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Role Modifiers")
    TMap<FString, float> RoleSafetyModifiers;

    // Advanced Safety
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Safety")
    bool bCheckForSnipers = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Safety")
    bool bAvoidOpenAreas = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Safety")
    float ThreatAssessmentRadius = 800.0f;

    // Team Coordination
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team Coordination")
    bool bRequestCoverFire = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team Coordination")
    bool bCoordinateWithNearbyAllies = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Team Coordination")
    float TeamCoordinationRadius = 600.0f;

    // Animation and Effects
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    class UAnimMontage* ReviveMontage;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    TMap<EReviveApproach, UAnimMontage*> ApproachSpecificMontages;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    class UParticleSystem* ReviveEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    class USoundBase* ReviveSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    class USoundBase* ReviveProgressSound;

    // Communication
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Communication")
    TArray<FString> ReviveStartCallouts;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Communication")
    TArray<FString> ReviveProgressCallouts;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Communication")
    TArray<FString> ReviveCompleteCallouts;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Communication")
    TArray<FString> ReviveFailedCallouts;

    // Debug and Logging
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bLogReviveEvents = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bDrawDebugInfo = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bLogTeamCoordination = false;

public:
    // Enhanced Static Utility Functions
    UFUNCTION(BlueprintCallable, Category = "Revive Enhanced", CallInEditor)
    static bool CanReviveTargetEnhanced(AActor* Reviver, AActor* Target, const FReviveConfiguration& Config);

    UFUNCTION(BlueprintCallable, Category = "Revive Enhanced", CallInEditor)
    static EReviveApproach DetermineOptimalApproach(AActor* Reviver, AActor* Target, float ThreatLevel);

    UFUNCTION(BlueprintCallable, Category = "Revive Enhanced", CallInEditor)
    static float CalculateReviveTime(AActor* Reviver, const FReviveConfiguration& Config);

    UFUNCTION(BlueprintCallable, Category = "Revive Enhanced", CallInEditor)
    static float AssessThreatLevel(const FVector& Location, float Radius);

protected:
    // Enhanced Core Logic
    bool InitializeEnhancedRevive(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    void UpdateEnhancedRevive(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    void CompleteEnhancedRevive(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EReviveResult Result);

    // Advanced Validation
    bool ValidateReviveConditions(AActor* Target, AActor* Reviver);
    bool AssessEnvironmentalSafety(const FVector& Position, AActor* Reviver);
    bool CheckTeammateAvailability(AActor* Reviver);
    float CalculateRoleModifiedReviveTime(AActor* Reviver);

    // Approach Strategy Implementation
    bool ExecuteDirectApproach(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    bool ExecuteCautiousApproach(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    bool ExecuteStealthApproach(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    bool ExecuteCoverToCoverApproach(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    bool ExecuteTeamCoordinatedApproach(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);

    // Team Coordination
    void RequestCoverFire(UBehaviorTreeComponent& OwnerComp, AActor* Target);
    void CoordinateWithNearbyAllies(UBehaviorTreeComponent& OwnerComp, AActor* Target);
    void BroadcastReviveIntent(UBehaviorTreeComponent& OwnerComp, AActor* Target);
    void UpdateTeamOnProgress(UBehaviorTreeComponent& OwnerComp, float Progress);

    // Advanced Safety Monitoring
    bool CheckForSniperThreats(const FVector& Position, float Radius);
    bool IsInOpenArea(const FVector& Position);
    float CalculatePositionExposure(const FVector& Position);
    TArray<AActor*> GetNearbyThreats(const FVector& Location, float Radius);

    // Enhanced Communication
    void PlayContextualCallout(UBehaviorTreeComponent& OwnerComp, const TArray<FString>& Callouts);
    FString GetRandomCallout(const TArray<FString>& Callouts);
    void BroadcastToSquadEnhanced(UBehaviorTreeComponent& OwnerComp, const FString& Message, float Priority);

private:
    // Enhanced Task Memory Structure
    struct FBTTask_ReviveAllyEnhancedMemory
    {
        TWeakObjectPtr<AActor> ReviveTarget;
        float ReviveStartTime = 0.0f;
        float LastSafetyCheck = 0.0f;
        float LastProgressUpdate = 0.0f;
        float ReviveProgress = 0.0f;
        float CalculatedReviveTime = 3.0f;
        float ThreatLevel = 0.0f;
        bool bReviveInProgress = false;
        bool bAnimationPlaying = false;
        bool bEffectsPlaying = false;
        bool bTeamCoordinated = false;
        bool bCoverFireRequested = false;
        FVector RevivePosition = FVector::ZeroVector;
        FVector SafeApproachPosition = FVector::ZeroVector;
        EReviveResult LastResult = EReviveResult::Success;
        EReviveApproach UsedApproach = EReviveApproach::Direct;
        TArray<TWeakObjectPtr<AActor>> CoordinatedAllies;
    };

    // Memory and utility functions
    FBTTask_ReviveAllyEnhancedMemory* GetEnhancedTaskMemory(uint8* NodeMemory);
    void InitializeEnhancedTaskMemory(uint8* NodeMemory);
    void CleanupEnhancedTaskMemory(uint8* NodeMemory);

    // Component access helpers
    UHealthComponent* GetHealthComponent(AActor* Actor);
    UReviveComponent* GetReviveComponent(AActor* Actor);
    ASquadMateAIController* GetSquadMateController(UBehaviorTreeComponent& OwnerComp);

    // Enhanced state management
    void SetEnhancedRevivingState(UBehaviorTreeComponent& OwnerComp, bool bIsReviving, EReviveApproach Approach);
    void UpdateBlackboardEnhancedState(UBehaviorTreeComponent& OwnerComp, const FBTTask_ReviveAllyEnhancedMemory* TaskMemory);

    // Debug and logging
    void LogEnhancedReviveEvent(const FString& Event, UBehaviorTreeComponent& OwnerComp, AActor* Target = nullptr);
    void DrawEnhancedDebugInfo(UWorld* World, const FBTTask_ReviveAllyEnhancedMemory* TaskMemory);
};
