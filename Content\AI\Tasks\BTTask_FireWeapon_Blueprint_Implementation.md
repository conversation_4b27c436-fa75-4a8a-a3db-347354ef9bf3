# 🔫 BTTask_FireWeapon - Blueprint Implementation Guide

## 📁 **Setup Instructions**

### 1. Create the Blueprint Task
1. Navigate to `Content/AI/Tasks/`
2. Right-click → **Blueprint Class**
3. **Parent Class**: Search for and select `BTTask_BlueprintBase`
4. **Name**: `BTTask_FireWeapon`

### 2. Configure Blackboard Keys

In the **Details Panel** of your Blueprint:

#### **Blackboard Key Selectors:**
- **TargetActor** (Object)
  - Key Type: `Object`
  - Base Class: `Actor`
  - Description: "The target to fire at"

- **FireMode** (Enum) - Optional
  - Key Type: `Enum`
  - Enum Type: `EFireMode`
  - Description: "Firing mode (Single, Burst, Auto)"

- **AmmoCount** (Int) - Optional
  - Key Type: `Int`
  - Description: "Current ammunition count"

## 🧠 **Blueprint Event Graph Implementation**

### **Event: Receive Execute AI**

```blueprint
[Event Receive Execute AI]
 ↓
[Get Blackboard Component]
 ↓
[Get Value As Object] (TargetActor Key)
 ↓
[Is Valid?] → [Branch]
 ↓ (True)
[Get Controlled Pawn]
 ↓
[Cast to SquadMateCharacter]
 ↓
[Is Valid?] → [Branch]
 ↓ (True)
[Custom Function: PerformLineOfSightCheck]
 ↓
[Branch] (Has Line of Sight?)
 ↓ (True)
[Custom Function: FireAtTarget]
 ↓
[Delay] (0.1 - 0.5 seconds for burst simulation)
 ↓
[Finish Execute] (Success)

// False branches lead to:
[Finish Execute] (Failed)
```

## 🎯 **Custom Functions to Implement**

### **Function: PerformLineOfSightCheck**
- **Input**: Target Actor (Actor Reference)
- **Output**: Has Line of Sight (Boolean)

```blueprint
[Get Controlled Pawn]
 ↓
[Get Actor Eyes View Point] (Location, Rotation)
 ↓
[Get Target Actor Location]
 ↓
[Line Trace By Channel]
  - Start: Eyes Location
  - End: Target Location
  - Trace Channel: Visibility
  - Ignore Self: True
 ↓
[Branch] (Was Hit?)
 ↓ (False = Clear line of sight)
[Return] True
 ↓ (True = Blocked)
[Get Hit Actor]
 ↓
[Equal (Object)] (Hit Actor == Target Actor)
 ↓
[Return] (True if hit target, False if blocked)
```

### **Function: FireAtTarget**
- **Input**: Target Actor (Actor Reference)
- **Output**: Fire Success (Boolean)

```blueprint
[Get Controlled Pawn]
 ↓
[Cast to SquadMateCharacter]
 ↓
[Call Function: GetWeaponMuzzleLocation]
 ↓
[Get Target Actor Location]
 ↓
[Vector - Vector] (Target - Muzzle = Direction)
 ↓
[Normalize] (Direction Vector)
 ↓
[Custom Function: ApplyAccuracySpread]
 ↓
[Line Trace By Channel]
  - Start: Muzzle Location
  - End: Target Location + Spread
  - Trace Channel: Visibility
 ↓
[Custom Function: PlayFireEffects]
 ↓
[Branch] (Hit Something?)
 ↓ (True)
[Custom Function: ApplyDamage]
 ↓
[Custom Function: ConsumeAmmo]
 ↓
[Return] True
```

## 🎮 **Enhanced Features**

### **Fire Mode Support**
Add a **Select** node based on FireMode enum:

```blueprint
[Get Blackboard] → [Get Value As Enum] (FireMode)
 ↓
[Select] (Fire Mode)
  - Single Shot: Fire once, finish
  - Burst Fire: Fire 3-5 shots with delays
  - Full Auto: Fire continuously for duration
  - Suppressive: Fire with reduced accuracy, high volume
```

### **Ammo Management**
```blueprint
[Get Blackboard] → [Get Value As Int] (AmmoCount)
 ↓
[Integer > Integer] (Ammo > 0)
 ↓ (True)
[Proceed with firing]
 ↓
[Integer - Integer] (Ammo - 1)
 ↓
[Set Blackboard Value As Int] (Update AmmoCount)
```

### **Audio/Visual Effects**
```blueprint
[Spawn Sound 2D] (Gunfire Sound)
 ↓
[Spawn Emitter at Location] (Muzzle Flash)
 ↓
[Spawn Emitter at Location] (Bullet Trail)
```

## 🔧 **Blueprint Variables**

### **Editable Variables:**
- **Fire Rate** (Float) = 0.1
  - Description: "Time between shots in seconds"
- **Burst Size** (Integer) = 3
  - Description: "Number of shots in burst mode"
- **Accuracy Modifier** (Float) = 0.95
  - Description: "Accuracy multiplier (1.0 = perfect)"
- **Max Fire Duration** (Float) = 2.0
  - Description: "Maximum continuous fire time"

### **Runtime Variables:**
- **Last Fire Time** (Float)
- **Shots Fired** (Integer)
- **Is Firing** (Boolean)

## 🎯 **Integration with C++ Backend**

Your Blueprint can call existing C++ functions:

```blueprint
[Get Controlled Pawn]
 ↓
[Cast to SquadMateCharacter]
 ↓
[Call Function: FireAtTarget] (C++ Implementation)
  - Input: Target Actor
  - Input: Fire Mode
  - Output: Success Boolean
```

## 🧪 **Testing Checklist**

- [ ] Task executes when TargetActor is valid
- [ ] Task fails gracefully when no target
- [ ] Line of sight check works correctly
- [ ] Fire effects play properly
- [ ] Ammo consumption works
- [ ] Different fire modes function
- [ ] Task completes with appropriate result

## 🔄 **Blueprint to C++ Parity**

This Blueprint implementation mirrors the existing C++ `BTTask_FireWeapon` class, ensuring:
- Same input parameters
- Same execution logic
- Same performance characteristics
- Easy migration between Blueprint and C++

## 📝 **Notes**

- Use **Blueprint Nativization** for better performance in shipping builds
- Consider **Blueprint Compilation** settings for optimization
- Test thoroughly in multiplayer scenarios
- Monitor performance with **Stat AI** console command
