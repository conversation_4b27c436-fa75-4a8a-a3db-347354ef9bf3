#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "GameplayTagContainer.h"
#include "HealthComponent.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnHealthChanged, float, CurrentHealth, float, MaxHealth);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnDeath, AActor*, DeadActor);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnRevived, AActor*, RevivedActor);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnDowned, AActor*, DownedActor);

UENUM(BlueprintType)
enum class EHealthState : uint8
{
    Healthy     UMETA(DisplayName = "Healthy"),
    Injured     UMETA(DisplayName = "Injured"),
    Critical    UMETA(DisplayName = "Critical"),
    Downed      UMETA(DisplayName = "Downed"),
    Dead        UMETA(DisplayName = "Dead")
};

USTRUCT(BlueprintType)
struct FDamageInfo
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float Amount = 0.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    AActor* Instigator = nullptr;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    AActor* DamageCauser = nullptr;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector HitLocation = FVector::ZeroVector;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FGameplayTagContainer DamageTags;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float Timestamp = 0.0f;

    FDamageInfo()
    {
        Amount = 0.0f;
        Instigator = nullptr;
        DamageCauser = nullptr;
        HitLocation = FVector::ZeroVector;
        Timestamp = 0.0f;
    }
};

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent), BlueprintType, Blueprintable)
class SQUADMATEAI_API UHealthComponent : public UActorComponent
{
    GENERATED_BODY()

public:
    UHealthComponent();

protected:
    virtual void BeginPlay() override;
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // Health Properties
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health", meta = (ClampMin = "0.0"))
    float MaxHealth = 100.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Health")
    float CurrentHealth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health", meta = (ClampMin = "0.0"))
    float DownedHealth = 25.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health")
    bool bCanBeRevived = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health")
    float ReviveTime = 3.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health")
    float BleedOutTime = 30.0f;

    // Regeneration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Regeneration")
    bool bCanRegenerate = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Regeneration")
    float RegenerationRate = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Regeneration")
    float RegenerationDelay = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Regeneration")
    float MaxRegenerationHealth = 75.0f;

    // Damage Resistance
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Resistance")
    TMap<FGameplayTag, float> DamageResistances;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Resistance")
    float ArmorValue = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Resistance")
    float ArmorDamageReduction = 0.5f;

    // State
    UPROPERTY(BlueprintReadOnly, Category = "State")
    EHealthState CurrentState;

    UPROPERTY(BlueprintReadOnly, Category = "State")
    bool bIsInvulnerable = false;

    UPROPERTY(BlueprintReadOnly, Category = "State")
    float LastDamageTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "State")
    float DownedTime = 0.0f;

    // Events
    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnHealthChanged OnHealthChanged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnDeath OnDeath;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnRevived OnRevived;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnDowned OnDowned;

    // Damage History
    UPROPERTY(BlueprintReadOnly, Category = "Debug")
    TArray<FDamageInfo> RecentDamage;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    int32 MaxDamageHistoryEntries = 10;

public:
    // Public Interface
    UFUNCTION(BlueprintCallable, Category = "Health")
    float TakeDamage(float DamageAmount, AActor* DamageInstigator = nullptr, AActor* DamageCauser = nullptr, 
                    FVector HitLocation = FVector::ZeroVector, FGameplayTagContainer DamageTags = FGameplayTagContainer());

    UFUNCTION(BlueprintCallable, Category = "Health")
    void Heal(float HealAmount);

    UFUNCTION(BlueprintCallable, Category = "Health")
    void SetHealth(float NewHealth);

    UFUNCTION(BlueprintCallable, Category = "Health")
    void SetMaxHealth(float NewMaxHealth);

    UFUNCTION(BlueprintCallable, Category = "Health")
    float GetHealth() const { return CurrentHealth; }

    UFUNCTION(BlueprintCallable, Category = "Health")
    float GetMaxHealth() const { return MaxHealth; }

    UFUNCTION(BlueprintCallable, Category = "Health")
    float GetHealthPercentage() const;

    UFUNCTION(BlueprintCallable, Category = "Health")
    bool IsAlive() const { return CurrentState != EHealthState::Dead; }

    UFUNCTION(BlueprintCallable, Category = "Health")
    bool IsDead() const { return CurrentState == EHealthState::Dead; }

    UFUNCTION(BlueprintCallable, Category = "Health")
    bool IsDown() const { return CurrentState == EHealthState::Downed; }

    UFUNCTION(BlueprintCallable, Category = "Health")
    bool IsHealthy() const { return CurrentState == EHealthState::Healthy; }

    UFUNCTION(BlueprintCallable, Category = "Health")
    bool IsInjured() const { return CurrentState == EHealthState::Injured || CurrentState == EHealthState::Critical; }

    UFUNCTION(BlueprintCallable, Category = "Health")
    EHealthState GetHealthState() const { return CurrentState; }

    // Revival System
    UFUNCTION(BlueprintCallable, Category = "Revival")
    bool CanBeRevived() const;

    UFUNCTION(BlueprintCallable, Category = "Revival")
    void StartRevive(AActor* Reviver);

    UFUNCTION(BlueprintCallable, Category = "Revival")
    void CompleteRevive();

    UFUNCTION(BlueprintCallable, Category = "Revival")
    void CancelRevive();

    UFUNCTION(BlueprintCallable, Category = "Revival")
    bool IsBeingRevived() const { return CurrentReviver != nullptr; }

    UFUNCTION(BlueprintCallable, Category = "Revival")
    AActor* GetReviver() const { return CurrentReviver; }

    UFUNCTION(BlueprintCallable, Category = "Revival")
    float GetReviveProgress() const;

    // Armor System
    UFUNCTION(BlueprintCallable, Category = "Armor")
    void SetArmor(float NewArmorValue);

    UFUNCTION(BlueprintCallable, Category = "Armor")
    float GetArmor() const { return ArmorValue; }

    UFUNCTION(BlueprintCallable, Category = "Armor")
    void DamageArmor(float ArmorDamage);

    // Invulnerability
    UFUNCTION(BlueprintCallable, Category = "Health")
    void SetInvulnerable(bool bNewInvulnerable);

    UFUNCTION(BlueprintCallable, Category = "Health")
    bool IsInvulnerable() const { return bIsInvulnerable; }

    // Damage History
    UFUNCTION(BlueprintCallable, Category = "Debug")
    TArray<FDamageInfo> GetRecentDamage() const { return RecentDamage; }

    UFUNCTION(BlueprintCallable, Category = "Debug")
    AActor* GetLastDamageInstigator() const;

    UFUNCTION(BlueprintCallable, Category = "Debug")
    float GetTimeSinceLastDamage() const;

protected:
    // Internal Methods
    void UpdateHealthState();
    void ProcessRegeneration(float DeltaTime);
    void ProcessBleedOut(float DeltaTime);
    float CalculateDamageReduction(float IncomingDamage, FGameplayTagContainer DamageTags);
    void AddDamageToHistory(const FDamageInfo& DamageInfo);
    void CleanupDamageHistory();

    // State Transitions
    void OnEnterDowned();
    void OnEnterDead();
    void OnEnterHealthy();
    void OnEnterInjured();
    void OnEnterCritical();

    // Revival System
    AActor* CurrentReviver = nullptr;
    float ReviveStartTime = 0.0f;
    FTimerHandle ReviveTimerHandle;
    FTimerHandle BleedOutTimerHandle;

    UFUNCTION()
    void OnReviveTimerComplete();

    UFUNCTION()
    void OnBleedOutTimerComplete();

private:
    // Internal State
    float LastRegenerationTime = 0.0f;
    bool bWasRecentlyDamaged = false;
    
    // Utility Functions
    void BroadcastHealthChanged();
    void InitializeHealth();
    bool ShouldRegenerate() const;
    EHealthState DetermineHealthState(float Health) const;
};
