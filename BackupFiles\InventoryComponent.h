#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "GameplayTagContainer.h"
#include "InventoryComponent.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAmmoChanged, int32, CurrentAmmo, int32, MaxAmmo);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnWeaponChanged, FString, WeaponName, int32, WeaponIndex);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnItemPickedUp, FString, ItemName);

UENUM(BlueprintType)
enum class EWeaponType : uint8
{
    AssaultRifle    UMETA(DisplayName = "Assault Rifle"),
    SniperRifle     UMETA(DisplayName = "Sniper Rifle"),
    SMG             UMETA(DisplayName = "SMG"),
    Shotgun         UMETA(DisplayName = "Shotgun"),
    Pistol          UMETA(DisplayName = "Pistol"),
    LMG             UMETA(DisplayName = "LMG"),
    Grenade         UMETA(DisplayName = "Grenade")
};

UENUM(BlueprintType)
enum class EItemType : uint8
{
    Weapon          UMETA(DisplayName = "Weapon"),
    Ammo            UMETA(DisplayName = "Ammo"),
    Medical         UMETA(DisplayName = "Medical"),
    Armor           UMETA(DisplayName = "Armor"),
    Utility         UMETA(DisplayName = "Utility"),
    Attachment      UMETA(DisplayName = "Attachment")
};

USTRUCT(BlueprintType)
struct FWeaponInfo
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString WeaponName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EWeaponType WeaponType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 Damage = 30;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float Range = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float FireRate = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 MagazineSize = 30;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float ReloadTime = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIsAutomatic = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float Accuracy = 0.9f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float Recoil = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    UStaticMesh* WeaponMesh = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FGameplayTagContainer WeaponTags;

    FWeaponInfo()
    {
        WeaponName = TEXT("Default Weapon");
        WeaponType = EWeaponType::AssaultRifle;
        Damage = 30;
        Range = 1000.0f;
        FireRate = 0.1f;
        MagazineSize = 30;
        ReloadTime = 2.0f;
        bIsAutomatic = true;
        Accuracy = 0.9f;
        Recoil = 0.1f;
        WeaponMesh = nullptr;
    }
};

USTRUCT(BlueprintType)
struct FInventoryItem
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString ItemName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EItemType ItemType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 Quantity = 1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 MaxStack = 1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FGameplayTagContainer ItemTags;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    UTexture2D* ItemIcon = nullptr;

    FInventoryItem()
    {
        ItemName = TEXT("Default Item");
        ItemType = EItemType::Utility;
        Quantity = 1;
        MaxStack = 1;
        ItemIcon = nullptr;
    }
};

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent), BlueprintType, Blueprintable)
class SQUADMATEAI_API UInventoryComponent : public UActorComponent
{
    GENERATED_BODY()

public:
    UInventoryComponent();

protected:
    virtual void BeginPlay() override;
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // Weapon System
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapons")
    TArray<FWeaponInfo> AvailableWeapons;

    UPROPERTY(BlueprintReadOnly, Category = "Weapons")
    TArray<FWeaponInfo> OwnedWeapons;

    UPROPERTY(BlueprintReadOnly, Category = "Weapons")
    int32 CurrentWeaponIndex = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapons")
    int32 MaxWeaponSlots = 2;

    // Ammo System
    UPROPERTY(BlueprintReadOnly, Category = "Ammo")
    TMap<EWeaponType, int32> AmmoInventory;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ammo")
    TMap<EWeaponType, int32> MaxAmmoCapacity;

    UPROPERTY(BlueprintReadOnly, Category = "Ammo")
    int32 CurrentMagazineAmmo;

    // General Inventory
    UPROPERTY(BlueprintReadOnly, Category = "Inventory")
    TArray<FInventoryItem> InventoryItems;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Inventory")
    int32 MaxInventorySlots = 20;

    // Medical Items
    UPROPERTY(BlueprintReadOnly, Category = "Medical")
    int32 MedkitCount = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Medical")
    int32 BandageCount = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Medical")
    int32 PainkillersCount = 0;

    // Armor
    UPROPERTY(BlueprintReadOnly, Category = "Armor")
    float ArmorLevel = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Armor")
    float MaxArmorLevel = 100.0f;

    // Events
    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnAmmoChanged OnAmmoChanged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnWeaponChanged OnWeaponChanged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnItemPickedUp OnItemPickedUp;

public:
    // Weapon Management
    UFUNCTION(BlueprintCallable, Category = "Weapons")
    bool PickupWeapon(const FWeaponInfo& WeaponInfo);

    UFUNCTION(BlueprintCallable, Category = "Weapons")
    void DropWeapon(int32 WeaponIndex);

    UFUNCTION(BlueprintCallable, Category = "Weapons")
    void SwitchWeapon(int32 WeaponIndex);

    UFUNCTION(BlueprintCallable, Category = "Weapons")
    void SwitchToNextWeapon();

    UFUNCTION(BlueprintCallable, Category = "Weapons")
    void SwitchToPreviousWeapon();

    UFUNCTION(BlueprintCallable, Category = "Weapons")
    FWeaponInfo GetCurrentWeapon() const;

    UFUNCTION(BlueprintCallable, Category = "Weapons")
    bool HasWeapon(const FString& WeaponName) const;

    UFUNCTION(BlueprintCallable, Category = "Weapons")
    int32 GetWeaponCount() const { return OwnedWeapons.Num(); }

    UFUNCTION(BlueprintCallable, Category = "Weapons")
    TArray<FWeaponInfo> GetOwnedWeapons() const { return OwnedWeapons; }

    // Ammo Management
    UFUNCTION(BlueprintCallable, Category = "Ammo")
    bool ConsumeAmmo(int32 Amount = 1);

    UFUNCTION(BlueprintCallable, Category = "Ammo")
    bool CanReload() const;

    UFUNCTION(BlueprintCallable, Category = "Ammo")
    void Reload();

    UFUNCTION(BlueprintCallable, Category = "Ammo")
    void AddAmmo(EWeaponType WeaponType, int32 Amount);

    UFUNCTION(BlueprintCallable, Category = "Ammo")
    int32 GetCurrentAmmo() const { return CurrentMagazineAmmo; }

    UFUNCTION(BlueprintCallable, Category = "Ammo")
    int32 GetReserveAmmo(EWeaponType WeaponType) const;

    UFUNCTION(BlueprintCallable, Category = "Ammo")
    int32 GetTotalAmmo(EWeaponType WeaponType) const;

    UFUNCTION(BlueprintCallable, Category = "Ammo")
    bool HasAmmo(EWeaponType WeaponType) const;

    // Item Management
    UFUNCTION(BlueprintCallable, Category = "Items")
    bool AddItem(const FInventoryItem& Item);

    UFUNCTION(BlueprintCallable, Category = "Items")
    bool RemoveItem(const FString& ItemName, int32 Quantity = 1);

    UFUNCTION(BlueprintCallable, Category = "Items")
    bool HasItem(const FString& ItemName, int32 MinQuantity = 1) const;

    UFUNCTION(BlueprintCallable, Category = "Items")
    int32 GetItemQuantity(const FString& ItemName) const;

    UFUNCTION(BlueprintCallable, Category = "Items")
    TArray<FInventoryItem> GetInventoryItems() const { return InventoryItems; }

    UFUNCTION(BlueprintCallable, Category = "Items")
    bool IsInventoryFull() const;

    // Medical Items
    UFUNCTION(BlueprintCallable, Category = "Medical")
    bool UseMedkit();

    UFUNCTION(BlueprintCallable, Category = "Medical")
    bool UseBandage();

    UFUNCTION(BlueprintCallable, Category = "Medical")
    bool UsePainkillers();

    UFUNCTION(BlueprintCallable, Category = "Medical")
    void AddMedkit(int32 Count = 1);

    UFUNCTION(BlueprintCallable, Category = "Medical")
    void AddBandage(int32 Count = 1);

    UFUNCTION(BlueprintCallable, Category = "Medical")
    void AddPainkillers(int32 Count = 1);

    UFUNCTION(BlueprintCallable, Category = "Medical")
    int32 GetMedkitCount() const { return MedkitCount; }

    UFUNCTION(BlueprintCallable, Category = "Medical")
    int32 GetBandageCount() const { return BandageCount; }

    UFUNCTION(BlueprintCallable, Category = "Medical")
    int32 GetPainkillersCount() const { return PainkillersCount; }

    // Armor Management
    UFUNCTION(BlueprintCallable, Category = "Armor")
    void SetArmor(float NewArmorLevel);

    UFUNCTION(BlueprintCallable, Category = "Armor")
    void AddArmor(float ArmorAmount);

    UFUNCTION(BlueprintCallable, Category = "Armor")
    void DamageArmor(float DamageAmount);

    UFUNCTION(BlueprintCallable, Category = "Armor")
    float GetArmor() const { return ArmorLevel; }

    UFUNCTION(BlueprintCallable, Category = "Armor")
    float GetArmorPercentage() const;

    // AI Interface
    UFUNCTION(BlueprintCallable, Category = "AI")
    EWeaponType GetBestWeaponForRange(float Range) const;

    UFUNCTION(BlueprintCallable, Category = "AI")
    bool ShouldSwitchWeapon(float TargetRange) const;

    UFUNCTION(BlueprintCallable, Category = "AI")
    bool NeedsAmmo() const;

    UFUNCTION(BlueprintCallable, Category = "AI")
    bool NeedsMedicalSupplies() const;

    UFUNCTION(BlueprintCallable, Category = "AI")
    float GetWeaponEffectiveness(const FWeaponInfo& Weapon, float Range) const;

protected:
    // Internal Methods
    void InitializeInventory();
    void InitializeDefaultWeapons();
    void InitializeAmmoCapacities();
    
    bool CanPickupWeapon(const FWeaponInfo& WeaponInfo) const;
    int32 FindWeaponIndex(const FString& WeaponName) const;
    int32 FindItemIndex(const FString& ItemName) const;
    
    void UpdateAmmoDisplay();
    void UpdateWeaponDisplay();
    
    // Weapon Utilities
    bool IsValidWeaponIndex(int32 Index) const;
    EWeaponType GetCurrentWeaponType() const;
    
    // Item Utilities
    bool CanStackItem(const FInventoryItem& ExistingItem, const FInventoryItem& NewItem) const;
    void StackItem(int32 ExistingIndex, const FInventoryItem& NewItem);

private:
    // Internal State
    bool bIsReloading = false;
    float LastReloadTime = 0.0f;
    
    // Default weapon configurations
    void SetupDefaultWeapons();
    FWeaponInfo CreateWeaponInfo(const FString& Name, EWeaponType Type, int32 Damage, 
                                float Range, float FireRate, int32 MagSize, float ReloadTime, bool bAuto);
};
