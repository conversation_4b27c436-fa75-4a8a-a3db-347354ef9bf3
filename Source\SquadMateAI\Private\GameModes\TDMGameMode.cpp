#include "GameModes/TDMGameMode.h"
#include "AI/TDMAIController.h"
#include "Characters/VictorCharacter.h"
#include "Systems/TDMMatchManager.h"
#include "Systems/TDMSpawnSystem.h"
#include "Components/HealthComponent.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "GameFramework/PlayerStart.h"
#include "Kismet/GameplayStatics.h"

ATDMGameMode::ATDMGameMode()
{
    PrimaryActorTick.bCanEverTick = true;

    // Set default classes
    DefaultPawnClass = AVictorCharacter::StaticClass();
    PlayerControllerClass = ATDMAIController::StaticClass();

    // Initialize match state
    CurrentMatchState = ETDMMatchState::WaitingToStart;
    MatchTimeRemaining = 0.0f;
    MatchStartTime = 0.0f;
    bMatchPaused = false;

    // Set default match configuration
    MatchConfig.KillLimit = 40;
    MatchConfig.TimeLimit = 600.0f; // 10 minutes
    MatchConfig.RespawnDelay = 3.0f;
    MatchConfig.SpawnProtectionTime = 3.0f;
    MatchConfig.bInfiniteAmmo = true;
    MatchConfig.bAutoPickup = true;

    // Initialize allowed weapons for PUBGM TDM
    MatchConfig.AllowedWeapons.Add(ETDMWeaponType::AR_M416);
    MatchConfig.AllowedWeapons.Add(ETDMWeaponType::AR_AKM);
    MatchConfig.AllowedWeapons.Add(ETDMWeaponType::AR_SCAR);
    MatchConfig.AllowedWeapons.Add(ETDMWeaponType::SMG_UMP45);
    MatchConfig.AllowedWeapons.Add(ETDMWeaponType::SMG_UZI);
    MatchConfig.AllowedWeapons.Add(ETDMWeaponType::SMG_VECTOR);
    MatchConfig.AllowedWeapons.Add(ETDMWeaponType::SHOTGUN_S12K);
}

void ATDMGameMode::BeginPlay()
{
    Super::BeginPlay();

    InitializeTeams();
    InitializeSpawnSystem();
    SpawnAIPlayers();
    
    // Start match after brief delay
    FTimerHandle StartDelayHandle;
    GetWorld()->GetTimerManager().SetTimer(
        StartDelayHandle,
        this,
        &ATDMGameMode::StartMatch,
        2.0f,
        false
    );
}

void ATDMGameMode::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    if (IsMatchInProgress() && !bMatchPaused)
    {
        UpdateMatchTimer();
        CheckWinConditions();
    }
}

void ATDMGameMode::InitializeTeams()
{
    // Create Team 1 (Blue)
    FTDMTeamInfo Team1;
    Team1.TeamID = 0;
    Team1.TeamName = TEXT("Blue Team");
    Team1.TeamColor = FLinearColor::Blue;
    Team1.KillCount = 0;
    Teams.Add(Team1);

    // Create Team 2 (Red)
    FTDMTeamInfo Team2;
    Team2.TeamID = 1;
    Team2.TeamName = TEXT("Red Team");
    Team2.TeamColor = FLinearColor::Red;
    Team2.KillCount = 0;
    Teams.Add(Team2);
}

void ATDMGameMode::InitializeSpawnSystem()
{
    // Create spawn system if it doesn't exist
    if (!SpawnSystem)
    {
        SpawnSystem = GetWorld()->SpawnActor<ATDMSpawnSystem>();
        if (SpawnSystem)
        {
            SpawnSystem->InitializeSpawnPoints();
        }
    }
}

void ATDMGameMode::SpawnAIPlayers()
{
    // Spawn 5 AI players for each team
    for (int32 TeamID = 0; TeamID < 2; TeamID++)
    {
        for (int32 PlayerIndex = 0; PlayerIndex < 5; PlayerIndex++)
        {
            ETDMRole Role = static_cast<ETDMRole>(PlayerIndex % 5); // Cycle through roles
            SpawnPlayer(TeamID, Role);
        }
    }
}

void ATDMGameMode::SpawnPlayer(int32 TeamID, ETDMRole Role)
{
    if (!VictorCharacterClass || !TDMAIControllerClass)
        return;

    // Get spawn location
    FVector SpawnLocation = GetSpawnLocation(TeamID);
    FRotator SpawnRotation = GetSpawnRotation(TeamID);

    // Spawn the character
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    AVictorCharacter* NewCharacter = GetWorld()->SpawnActor<AVictorCharacter>(
        VictorCharacterClass,
        SpawnLocation,
        SpawnRotation,
        SpawnParams
    );

    if (NewCharacter)
    {
        // Spawn and possess with AI controller
        ATDMAIController* AIController = GetWorld()->SpawnActor<ATDMAIController>(TDMAIControllerClass);
        if (AIController)
        {
            AIController->Possess(NewCharacter);
            ConfigureAIPlayer(NewCharacter, TeamID, Role);
        }

        // Add to team
        if (Teams.IsValidIndex(TeamID))
        {
            Teams[TeamID].TeamMembers.Add(NewCharacter);
        }

        // Initialize player stats
        InitializePlayerStats(NewCharacter);
    }
}

void ATDMGameMode::ConfigureAIPlayer(APawn* AIPlayer, int32 TeamID, ETDMRole Role)
{
    if (!AIPlayer)
        return;

    ATDMAIController* AIController = Cast<ATDMAIController>(AIPlayer->GetController());
    if (!AIController)
        return;

    // Set team and role
    AIController->TeamID = TeamID;
    AIController->SetRole(Role);

    // Configure loadout based on role
    FTDMLoadout Loadout;
    switch (Role)
    {
        case ETDMRole::Assault:
            Loadout.PrimaryWeapon = ETDMWeaponType::AR_M416;
            Loadout.SecondaryWeapon = ETDMWeaponType::SMG_UMP45;
            break;
        case ETDMRole::Support:
            Loadout.PrimaryWeapon = ETDMWeaponType::AR_SCAR;
            Loadout.SecondaryWeapon = ETDMWeaponType::SMG_VECTOR;
            break;
        case ETDMRole::Sniper:
            Loadout.PrimaryWeapon = ETDMWeaponType::SNIPER_KAR98;
            Loadout.SecondaryWeapon = ETDMWeaponType::AR_AKM;
            break;
        case ETDMRole::Flanker:
            Loadout.PrimaryWeapon = ETDMWeaponType::SMG_UZI;
            Loadout.SecondaryWeapon = ETDMWeaponType::SHOTGUN_S12K;
            break;
        case ETDMRole::Entry:
            Loadout.PrimaryWeapon = ETDMWeaponType::SHOTGUN_S12K;
            Loadout.SecondaryWeapon = ETDMWeaponType::SMG_VECTOR;
            break;
    }

    AIController->SwitchLoadout(Loadout);

    // Set lane assignment based on role and team strategy
    ETDMLane AssignedLane = ETDMLane::Center;
    switch (Role)
    {
        case ETDMRole::Assault:
            AssignedLane = ETDMLane::Center;
            break;
        case ETDMRole::Support:
            AssignedLane = ETDMLane::Center;
            break;
        case ETDMRole::Sniper:
            AssignedLane = ETDMLane::Center; // Back of center for overwatch
            break;
        case ETDMRole::Flanker:
            AssignedLane = (TeamID == 0) ? ETDMLane::Left : ETDMLane::Right;
            break;
        case ETDMRole::Entry:
            AssignedLane = (TeamID == 0) ? ETDMLane::Right : ETDMLane::Left;
            break;
    }

    AIController->SetLane(AssignedLane);
}

void ATDMGameMode::StartMatch()
{
    if (CurrentMatchState != ETDMMatchState::WaitingToStart)
        return;

    SetMatchState(ETDMMatchState::InProgress);
    MatchStartTime = GetWorld()->GetTimeSeconds();
    MatchTimeRemaining = MatchConfig.TimeLimit;

    SetupMatchTimer();

    // Notify all players
    OnMatchStateChanged.Broadcast(CurrentMatchState);
}

void ATDMGameMode::SetupMatchTimer()
{
    GetWorld()->GetTimerManager().SetTimer(
        MatchTimerHandle,
        this,
        &ATDMGameMode::UpdateMatchTimer,
        1.0f,
        true
    );

    GetWorld()->GetTimerManager().SetTimer(
        StatsUpdateHandle,
        this,
        &ATDMGameMode::UpdatePlayerStats,
        5.0f,
        true
    );
}

void ATDMGameMode::UpdateMatchTimer()
{
    if (!IsMatchInProgress() || bMatchPaused)
        return;

    MatchTimeRemaining = MatchConfig.TimeLimit - (GetWorld()->GetTimeSeconds() - MatchStartTime);

    if (MatchTimeRemaining <= 0.0f)
    {
        HandleTimeLimit();
    }
}

void ATDMGameMode::CheckWinConditions()
{
    // Check if any team reached kill limit
    for (const FTDMTeamInfo& Team : Teams)
    {
        if (Team.KillCount >= MatchConfig.KillLimit)
        {
            HandleKillLimit(Team.TeamID);
            return;
        }
    }
}

void ATDMGameMode::HandleKillLimit(int32 TeamID)
{
    EndMatch(TeamID);
}

void ATDMGameMode::HandleTimeLimit()
{
    // Determine winner by highest score
    int32 WinningTeamID = GetWinningTeamID();
    EndMatch(WinningTeamID);
}

void ATDMGameMode::EndMatch(int32 WinningTeamID)
{
    SetMatchState(ETDMMatchState::Finished);
    
    GetWorld()->GetTimerManager().ClearTimer(MatchTimerHandle);
    GetWorld()->GetTimerManager().ClearTimer(StatsUpdateHandle);

    OnMatchFinished.Broadcast(WinningTeamID);
}

void ATDMGameMode::RegisterKill(APawn* Victim, APawn* Killer)
{
    if (!Victim || !Killer)
        return;

    // Update team score
    int32 KillerTeamID = GetPlayerTeamID(Killer);
    if (Teams.IsValidIndex(KillerTeamID))
    {
        Teams[KillerTeamID].KillCount++;
        OnTeamScoreChanged.Broadcast(KillerTeamID);
    }

    // Update player stats
    UpdatePlayerKillStats(Killer);
    UpdatePlayerDeathStats(Victim);

    // Broadcast event
    OnPlayerKilled.Broadcast(Victim, Killer);

    // Schedule respawn
    FTimerHandle RespawnHandle;
    GetWorld()->GetTimerManager().SetTimer(
        RespawnHandle,
        [this, Victim]()
        {
            RespawnPlayer(Victim);
        },
        MatchConfig.RespawnDelay,
        false
    );
}

void ATDMGameMode::RespawnPlayer(APawn* Player)
{
    if (!Player)
        return;

    int32 TeamID = GetPlayerTeamID(Player);
    FVector SpawnLocation = GetSpawnLocation(TeamID);
    FRotator SpawnRotation = GetSpawnRotation(TeamID);

    // Teleport to spawn location
    Player->SetActorLocation(SpawnLocation);
    Player->SetActorRotation(SpawnRotation);

    // Reset health
    if (UHealthComponent* HealthComp = Player->FindComponentByClass<UHealthComponent>())
    {
        HealthComp->ResetHealth();
    }

    // Notify AI controller of respawn
    if (ATDMAIController* AIController = Cast<ATDMAIController>(Player->GetController()))
    {
        AIController->OnRespawn();
    }
}

FVector ATDMGameMode::GetSpawnLocation(int32 TeamID) const
{
    if (SpawnSystem)
    {
        return SpawnSystem->GetSpawnLocation(TeamID);
    }

    // Fallback spawn locations
    if (TeamID == 0)
    {
        return FVector(-1000.0f, 0.0f, 100.0f); // Blue team spawn
    }
    else
    {
        return FVector(1000.0f, 0.0f, 100.0f); // Red team spawn
    }
}

FRotator ATDMGameMode::GetSpawnRotation(int32 TeamID) const
{
    if (TeamID == 0)
    {
        return FRotator(0.0f, 0.0f, 0.0f); // Face forward
    }
    else
    {
        return FRotator(0.0f, 180.0f, 0.0f); // Face opposite direction
    }
}

int32 ATDMGameMode::GetPlayerTeamID(APawn* Player) const
{
    if (!Player)
        return -1;

    if (ATDMAIController* AIController = Cast<ATDMAIController>(Player->GetController()))
    {
        return AIController->TeamID;
    }

    return -1;
}

int32 ATDMGameMode::GetWinningTeamID() const
{
    int32 HighestScore = -1;
    int32 WinningTeam = -1;

    for (int32 i = 0; i < Teams.Num(); i++)
    {
        if (Teams[i].KillCount > HighestScore)
        {
            HighestScore = Teams[i].KillCount;
            WinningTeam = i;
        }
    }

    return WinningTeam;
}

int32 ATDMGameMode::GetTeamScore(int32 TeamID) const
{
    if (Teams.IsValidIndex(TeamID))
    {
        return Teams[TeamID].KillCount;
    }
    return 0;
}

void ATDMGameMode::SetMatchState(ETDMMatchState NewState)
{
    if (CurrentMatchState != NewState)
    {
        CurrentMatchState = NewState;
        OnMatchStateChanged.Broadcast(NewState);
    }
}
